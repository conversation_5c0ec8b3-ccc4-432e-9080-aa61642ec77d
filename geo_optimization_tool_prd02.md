# GeoPilot 产品说明与实现路径文档（PRD）

## 一、项目概述

**项目名称**：GeoPilot（Generative Engine Optimization Tool）

**项目目标**：
构建一款面向内容创作者和企业的 GEO（生成式搜索引擎优化）工具，帮助用户优化其内容结构与语义，提高在 ChatGPT、Google SGE、Bing Copilot、Perplexity 等平台中的可见度与引用率，从而实现引流、品牌曝光和信息主导权。

**目标用户**：
- SEO团队
- 品牌内容运营
- 独立创作者、自媒体人
- SaaS平台内容负责人
- 教育/知识博主

**平台形态**：
- Web 应用
- 支持 API 和插件接入（如 WordPress）

## 二、产品功能模块与实现路径

### 1. 内容 GEO 评分与优化建议
**目标**：分析用户输入内容，给出 GEO 优化分数和建议。
**实现路径**：
1. 获取文本（直接输入或抓取 URL 内容）
2. 文本清洗与分段
3. 规则+AI 结合评分（结构、引用性、语义清晰度等）
4. 使用 LLM 提供逐段优化建议
**技术**：Node.js/NestJS + OpenAI GPT-4o + LangChain + PostgreSQL

### 2. 结构化标注生成器
**目标**：生成 Schema.org JSON-LD 数据。
**实现路径**：
1. 用户选择标注类型（FAQ、HowTo、Product、Article 等）
2. 表单输入内容
3. schema-dts 生成 JSON-LD
4. 代码预览与一键复制
**技术**：React + schema-dts

### 3. 可引用段落提取与重构
**目标**：生成 AI 易引用的原子知识单元。
**实现路径**：
1. NLP 分段（spaCy/NLTK）
2. 段落引用价值打分
3. LLM 改写优化
4. 导出为 Prompt 模板
**技术**：spaCy / NLTK + OpenAI GPT-4o

### 4. GEO关键词与问题生成
**目标**：生成能触发 AI 使用的高潜力问题和关键词。
**实现路径**：

1. 提取主题关键词（TF-IDF / KeyBERT）
2. 模板生成问题
3. Google Suggest API 拓展长尾词
**技术**：KeyBERT + Google/Bing API

### 5. AI 内容重构引擎
**目标**：生成不同平台偏好的内容版本。
**实现路径**：
1. 平台模板（ChatGPT、Google SGE、Perplexity）
2. Prompt 优化内容结构
3. 支持批量处理
**技术**：OpenAI GPT-4o + LangChain Prompt Templates

### 6. 内容管理与报告中心
**目标**：可视化管理历史内容与优化前后对比。
**实现路径**：
1. 存储原文与优化版
2. React Diff Viewer 对比
3. 报告导出 PDF/CSV
**技术**：PostgreSQL + Chart.js + React Diff Viewer

### 7. GEO 曝光追踪系统
**目标**：检测用户内容是否被 AI 引用。
**实现路径**：
1. Prompt Pool 生成问题变体
2. 模拟提问至 ChatGPT/Perplexity/Bing/Claude/Google SGE
3. 使用向量匹配比对回答与原文
4. 记录引用信息并生成热力图、趋势图
**技术**：pgvector/Weaviate + OpenAI Embedding API + Chart.js/Superset

### 8. 用户系统与权限管理
**目标**：控制访问、配额与订阅功能。
**实现路径**：
1. JWT + OAuth2（Google、GitHub）
2. RBAC 权限分级
3. 基于角色限制调用次数
**技术**：Auth0/NextAuth.js + NestJS RBAC

### 9. 支付与订阅系统
**目标**：支持按需付费解锁高级功能。
**实现路径**：
1. 订阅计划配置（月/年付）
2. Stripe/PayPal API 集成
3. Webhook 同步支付状态
**技术**：Stripe API / PayPal API + NestJS

### 10. 多语言支持
**目标**：支持中英切换与跨语言优化。
**实现路径**：
1. i18next UI 国际化
2. AI 翻译 + GEO 优化
**技术**：i18next + OpenAI GPT/DeepL API

### 11. 数据导出与 API 集成
**目标**：提供 GEO 分析能力给第三方平台。
**实现路径**：
1. PDF（reportlab）、CSV（pandas）导出
2. REST API + API Key 认证
3. WordPress/Notion 插件开发
**技术**：NestJS REST + pdfkit/openpyxl

## 三、系统角色与权限
| 角色 | 权限 |
|------|------|
| 访客 | 浏览介绍、试用体验 |
| 注册用户 | 使用内容评分/优化功能，保存历史 |
| 高级用户（订阅） | 使用全部功能、导出报告、结构化标注、自定义Prompt、引用监测 |
| 管理员 | 管理用户、功能模块配置、模型配置、数据追踪、内容监控 |

## 四、技术架构
- 前端：React 或 Next.js + TailwindCSS
- 后端：Node.js + Express / NestJS
- 数据库：PostgreSQL + pgvector
- AI：OpenAI GPT-4o + LangChain
- 部署：Vercel / Railway / AWS（Serverless）
- 支付：Stripe/PayPal

## 五、商业模式
- Freemium：基础功能免费，高级功能订阅
- 增值服务：高级报告、改写次数提升、追踪频率升级、API配额提升、企业定制

