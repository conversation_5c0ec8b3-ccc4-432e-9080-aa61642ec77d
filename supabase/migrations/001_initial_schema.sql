-- GeoPilot Database Schema
-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VA<PERSON>HAR(255),
    avatar_url TEXT,
    subscription_type VARCHAR(50) DEFAULT 'free',
    subscription_expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建内容表
CREATE TABLE IF NOT EXISTS contents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    original_content TEXT NOT NULL,
    content_type VARCHAR(50) DEFAULT 'article',
    word_count INTEGER DEFAULT 0,
    language VARCHAR(10) DEFAULT 'zh',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建分析表
CREATE TABLE IF NOT EXISTS analyses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID NOT NULL REFERENCES contents(id) ON DELETE CASCADE,
    analysis_type VARCHAR(50) NOT NULL,
    result JSONB NOT NULL,
    score DECIMAL(3,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建建议表
CREATE TABLE IF NOT EXISTS suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID NOT NULL REFERENCES contents(id) ON DELETE CASCADE,
    suggestion_type VARCHAR(50) NOT NULL,
    original_text TEXT,
    suggested_text TEXT NOT NULL,
    reason TEXT,
    priority INTEGER DEFAULT 1,
    is_applied BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建追踪任务表
CREATE TABLE IF NOT EXISTS tracking_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID NOT NULL REFERENCES contents(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'pending',
    platforms JSONB DEFAULT '[]',
    queries JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建追踪结果表
CREATE TABLE IF NOT EXISTS tracking_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES tracking_tasks(id) ON DELETE CASCADE,
    platform VARCHAR(100) NOT NULL,
    query VARCHAR(500) NOT NULL,
    found_citation BOOLEAN DEFAULT FALSE,
    ai_response TEXT,
    similarity_score DECIMAL(3,2),
    citation_context TEXT,
    checked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建订阅表
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    plan_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    stripe_subscription_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建内容嵌入表
CREATE TABLE IF NOT EXISTS content_embeddings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID NOT NULL REFERENCES contents(id) ON DELETE CASCADE,
    embedding_vector TEXT,
    model_name VARCHAR(100) DEFAULT 'text-embedding-ada-002',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_contents_user_id ON contents(user_id);
CREATE INDEX IF NOT EXISTS idx_contents_created_at ON contents(created_at);
CREATE INDEX IF NOT EXISTS idx_analyses_content_id ON analyses(content_id);
CREATE INDEX IF NOT EXISTS idx_suggestions_content_id ON suggestions(content_id);
CREATE INDEX IF NOT EXISTS idx_tracking_tasks_content_id ON tracking_tasks(content_id);
CREATE INDEX IF NOT EXISTS idx_tracking_results_task_id ON tracking_results(task_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_content_embeddings_content_id ON content_embeddings(content_id);

-- 启用行级安全策略
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE contents ENABLE ROW LEVEL SECURITY;
ALTER TABLE analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE suggestions ENABLE ROW LEVEL SECURITY;
ALTER TABLE tracking_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE tracking_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_embeddings ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
-- 用户只能访问自己的数据
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own contents" ON contents FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own contents" ON contents FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own contents" ON contents FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own contents" ON contents FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own analyses" ON analyses FOR SELECT USING (auth.uid() = (SELECT user_id FROM contents WHERE id = content_id));
CREATE POLICY "Users can insert own analyses" ON analyses FOR INSERT WITH CHECK (auth.uid() = (SELECT user_id FROM contents WHERE id = content_id));

CREATE POLICY "Users can view own suggestions" ON suggestions FOR SELECT USING (auth.uid() = (SELECT user_id FROM contents WHERE id = content_id));
CREATE POLICY "Users can insert own suggestions" ON suggestions FOR INSERT WITH CHECK (auth.uid() = (SELECT user_id FROM contents WHERE id = content_id));
CREATE POLICY "Users can update own suggestions" ON suggestions FOR UPDATE USING (auth.uid() = (SELECT user_id FROM contents WHERE id = content_id));

CREATE POLICY "Users can view own tracking tasks" ON tracking_tasks FOR SELECT USING (auth.uid() = (SELECT user_id FROM contents WHERE id = content_id));
CREATE POLICY "Users can insert own tracking tasks" ON tracking_tasks FOR INSERT WITH CHECK (auth.uid() = (SELECT user_id FROM contents WHERE id = content_id));
CREATE POLICY "Users can update own tracking tasks" ON tracking_tasks FOR UPDATE USING (auth.uid() = (SELECT user_id FROM contents WHERE id = content_id));

CREATE POLICY "Users can view own tracking results" ON tracking_results FOR SELECT USING (auth.uid() = (SELECT user_id FROM contents WHERE id = (SELECT content_id FROM tracking_tasks WHERE id = task_id)));
CREATE POLICY "Users can insert own tracking results" ON tracking_results FOR INSERT WITH CHECK (auth.uid() = (SELECT user_id FROM contents WHERE id = (SELECT content_id FROM tracking_tasks WHERE id = task_id)));

CREATE POLICY "Users can view own subscriptions" ON subscriptions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own subscriptions" ON subscriptions FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own subscriptions" ON subscriptions FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own embeddings" ON content_embeddings FOR SELECT USING (auth.uid() = (SELECT user_id FROM contents WHERE id = content_id));
CREATE POLICY "Users can insert own embeddings" ON content_embeddings FOR INSERT WITH CHECK (auth.uid() = (SELECT user_id FROM contents WHERE id = content_id));

-- 授予权限
GRANT SELECT ON users TO anon, authenticated;
GRANT ALL PRIVILEGES ON users TO authenticated;

GRANT SELECT ON contents TO anon, authenticated;
GRANT ALL PRIVILEGES ON contents TO authenticated;

GRANT SELECT ON analyses TO anon, authenticated;
GRANT ALL PRIVILEGES ON analyses TO authenticated;

GRANT SELECT ON suggestions TO anon, authenticated;
GRANT ALL PRIVILEGES ON suggestions TO authenticated;

GRANT SELECT ON tracking_tasks TO anon, authenticated;
GRANT ALL PRIVILEGES ON tracking_tasks TO authenticated;

GRANT SELECT ON tracking_results TO anon, authenticated;
GRANT ALL PRIVILEGES ON tracking_results TO authenticated;

GRANT SELECT ON subscriptions TO anon, authenticated;
GRANT ALL PRIVILEGES ON subscriptions TO authenticated;

GRANT SELECT ON content_embeddings TO anon, authenticated;
GRANT ALL PRIVILEGES ON content_embeddings TO authenticated;