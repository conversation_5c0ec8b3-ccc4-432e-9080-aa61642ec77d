-- 检查当前权限设置
SELECT grantee, table_name, privilege_type 
FROM information_schema.role_table_grants 
WHERE table_schema = 'public' 
AND grantee IN ('anon', 'authenticated') 
ORDER BY table_name, grantee;

-- 为anon角色授予基本读取权限
GRANT SELECT ON users TO anon;
GRANT SELECT ON contents TO anon;
GRANT SELECT ON analyses TO anon;
GRANT SELECT ON suggestions TO anon;
GRANT SELECT ON tracking_tasks TO anon;
GRANT SELECT ON tracking_results TO anon;
GRANT SELECT ON subscriptions TO anon;
GRANT SELECT ON content_embeddings TO anon;

-- 为authenticated角色授予完整权限
GRANT ALL PRIVILEGES ON users TO authenticated;
GRANT ALL PRIVILEGES ON contents TO authenticated;
GRANT ALL PRIVILEGES ON analyses TO authenticated;
GRANT ALL PRIVILEGES ON suggestions TO authenticated;
GRANT ALL PRIVILEGES ON tracking_tasks TO authenticated;
GRANT ALL PRIVILEGES ON tracking_results TO authenticated;
GRANT ALL PRIVILEGES ON subscriptions TO authenticated;
GRANT ALL PRIVILEGES ON content_embeddings TO authenticated;

-- 再次检查权限设置
SELECT grantee, table_name, privilege_type 
FROM information_schema.role_table_grants 
WHERE table_schema = 'public' 
AND grantee IN ('anon', 'authenticated') 
ORDER BY table_name, grantee;