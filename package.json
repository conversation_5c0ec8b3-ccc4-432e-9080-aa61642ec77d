{"name": "geopolit", "private": true, "version": "0.0.0", "type": "module", "scripts": {"client:dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "check": "tsc --noEmit", "server:dev": "cd api && npm run start:dev", "dev": "concurrently \"npm run client:dev\" \"npm run server:dev\"", "dev:frontend": "vite", "dev:backend": "cd api && npm run start:dev", "build:backend": "cd api && npm run build"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/typeorm": "^10.0.0", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "langchain": "^0.1.0", "lucide-react": "^0.511.0", "openai": "^4.0.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.3.0", "redis": "^4.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "sonner": "^2.0.7", "stripe": "^14.0.0", "tailwind-merge": "^3.0.2", "typeorm": "^0.3.17", "uuid": "^9.0.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/jest": "^29.5.0", "@types/node": "^22.15.30", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/pg": "^8.10.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/supertest": "^6.0.0", "@types/uuid": "^9.0.0", "@vercel/node": "^5.3.6", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "babel-plugin-react-dev-locator": "^1.0.0", "concurrently": "^9.2.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jest": "^29.5.0", "nodemon": "^3.1.10", "postcss": "^8.5.3", "supertest": "^6.3.0", "tailwindcss": "^3.4.17", "ts-jest": "^29.1.0", "ts-loader": "^9.4.0", "ts-node": "^10.9.0", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-trae-solo-badge": "^1.0.0", "vite-tsconfig-paths": "^5.1.4"}}