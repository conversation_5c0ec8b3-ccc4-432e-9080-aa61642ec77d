import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { Content } from './content.entity';
import { Suggestion } from './suggestion.entity';

@Entity('analyses')
export class Analysis {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'content_id' })
  contentId: string;

  @Column({ type: 'text', default: '{}' })
  scores: string;

  @Column({ type: 'text', name: 'analysis_result', nullable: true })
  analysisResult?: string;

  @Column({ name: 'model_version', default: 'gpt-4o' })
  modelVersion: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => Content, content => content.analyses, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'content_id' })
  content: Content;

  @OneToMany(() => Suggestion, suggestion => suggestion.analysis)
  suggestions: Suggestion[];
}