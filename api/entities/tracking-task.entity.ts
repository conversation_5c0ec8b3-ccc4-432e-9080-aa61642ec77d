import { <PERSON>tity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { Content } from './content.entity';
import { TrackingResult } from './tracking-result.entity';

export enum TrackingStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

@Entity('tracking_tasks')
export class TrackingTask {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'content_id' })
  contentId: string;

  @Column({
    type: 'varchar',
    default: TrackingStatus.PENDING,
  })
  status: TrackingStatus;

  @Column({ type: 'text', default: '[]' })
  platforms: string;

  @Column({ type: 'text', default: '[]' })
  queries: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => Content, content => content.trackingTasks, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'content_id' })
  content: Content;

  @OneToMany(() => TrackingResult, result => result.task)
  results: TrackingResult[];
}