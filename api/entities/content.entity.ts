import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { User } from './user.entity';
import { Analysis } from './analysis.entity';
import { TrackingTask } from './tracking-task.entity';
import { ContentEmbedding } from './content-embedding.entity';

@Entity('contents')
export class Content {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ length: 500 })
  title: string;

  @Column({ type: 'text', name: 'original_content' })
  originalContent: string;

  @Column({ type: 'text', name: 'optimized_content', nullable: true })
  optimizedContent?: string;

  @Column({ name: 'content_type', default: 'article' })
  contentType: string;

  @Column({ name: 'source_url', length: 1000, nullable: true })
  sourceUrl?: string;

  @Column({ type: 'text', default: '{}' })
  metadata: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => User, user => user.contents, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => Analysis, analysis => analysis.content)
  analyses: Analysis[];

  @OneToMany(() => TrackingTask, trackingTask => trackingTask.content)
  trackingTasks: TrackingTask[];

  @OneToMany(() => ContentEmbedding, embedding => embedding.content)
  embeddings: ContentEmbedding[];
}