import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Content } from './content.entity';
import { Subscription } from './subscription.entity';

export enum UserPlan {
  FREE = 'free',
  BASIC = 'basic',
  PREMIUM = 'premium',
  ENTERPRISE = 'enterprise',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column({ name: 'password_hash' })
  passwordHash: string;

  @Column({ nullable: true })
  name: string;

  @Column({ name: 'avatar_url', nullable: true })
  avatarUrl: string;

  @Column({
    name: 'subscription_type',
    type: 'varchar',
    default: UserPlan.FREE,
  })
  subscriptionType: UserPlan;

  @Column({ name: 'subscription_expires_at', nullable: true })
  subscriptionExpiresAt: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @OneToMany(() => Content, content => content.user)
  contents: Content[];

  @OneToMany(() => Subscription, subscription => subscription.user)
  subscriptions: Subscription[];

  // 为了向后兼容，添加getter和setter
  get plan(): UserPlan {
    return this.subscriptionType;
  }

  set plan(value: UserPlan) {
    this.subscriptionType = value;
  }

  // 虚拟字段：使用量计数（从contents关系计算）
  get usageCount(): number {
    return this.contents ? this.contents.length : 0;
  }
}