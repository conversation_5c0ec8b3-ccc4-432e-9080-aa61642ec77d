import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { TrackingTask } from './tracking-task.entity';

@Entity('tracking_results')
export class TrackingResult {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'task_id' })
  taskId: string;

  @Column()
  platform: string;

  @Column({ type: 'text' })
  query: string;

  @Column({ name: 'found_citation', default: false })
  foundCitation: boolean;

  @Column({ type: 'text', name: 'ai_response', nullable: true })
  aiResponse?: string;

  @Column({ type: 'float', name: 'similarity_score', default: 0.0 })
  similarityScore: number;

  @CreateDateColumn({ name: 'checked_at' })
  checkedAt: Date;

  @ManyToOne(() => TrackingTask, task => task.results, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'task_id' })
  task: TrackingTask;
}