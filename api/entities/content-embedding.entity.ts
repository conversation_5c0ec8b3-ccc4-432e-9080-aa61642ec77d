import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Content } from './content.entity';

@Entity('content_embeddings')
export class ContentEmbedding {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'content_id' })
  contentId: string;

  @Column({ type: 'text' })
  embedding: string; // 存储为JSON字符串，因为TypeORM不直接支持vector类型

  @Column({ name: 'model_name', default: 'text-embedding-ada-002' })
  modelName: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => Content, content => content.embeddings, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'content_id' })
  content: Content;
}