import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Analysis } from './analysis.entity';

@Entity('suggestions')
export class Suggestion {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'analysis_id' })
  analysisId: string;

  @Column({ name: 'suggestion_type' })
  suggestionType: string;

  @Column({ type: 'text', name: 'original_text' })
  originalText: string;

  @Column({ type: 'text', name: 'suggested_text' })
  suggestedText: string;

  @Column({ type: 'float', name: 'confidence_score', default: 0.0 })
  confidenceScore: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => Analysis, analysis => analysis.suggestions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'analysis_id' })
  analysis: Analysis;
}