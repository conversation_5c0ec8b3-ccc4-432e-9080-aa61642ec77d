import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';
import { User } from '../entities/user.entity';
import { Content } from '../entities/content.entity';
import { Analysis } from '../entities/analysis.entity';
import { Suggestion } from '../entities/suggestion.entity';
import { TrackingTask } from '../entities/tracking-task.entity';
import { TrackingResult } from '../entities/tracking-result.entity';
import { Subscription } from '../entities/subscription.entity';
import { ContentEmbedding } from '../entities/content-embedding.entity';

@Injectable()
export class DatabaseConfig implements TypeOrmOptionsFactory {
  constructor(private configService: ConfigService) {}

  createTypeOrmOptions(): TypeOrmModuleOptions {
    const nodeEnv = this.configService.get<string>('NODE_ENV');

    // Use SQLite for development when Supabase is not accessible
    if (nodeEnv === 'development' || nodeEnv !== 'production') {
      return {
        type: 'sqlite',
        database: './database.sqlite',
        entities: [
          User,
          Content,
          Analysis,
          Suggestion,
          TrackingTask,
          TrackingResult,
          Subscription,
          ContentEmbedding,
        ],
        synchronize: true, // Auto-create tables in development
        logging: true,
        autoLoadEntities: true,
      };
    }

    // Production: Use Supabase PostgreSQL
    return {
      type: 'postgres',
      host: 'db.dqztjvbcduwcribwlxcb.supabase.co',
      port: 5432,
      username: 'postgres',
      password: 'j7Fhiz!z76YLC3H',
      database: 'postgres',
      entities: [
        User,
        Content,
        Analysis,
        Suggestion,
        TrackingTask,
        TrackingResult,
        Subscription,
        ContentEmbedding,
      ],
      synchronize: false,
      logging: true,
      ssl: {
        rejectUnauthorized: false,
      },
      retryAttempts: 3,
      retryDelay: 3000,
      autoLoadEntities: true,
    };
  }
}