import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions, TypeOrmOptionsFactory } from '@nestjs/typeorm';
import { User } from '../entities/user.entity';
import { Content } from '../entities/content.entity';
import { Analysis } from '../entities/analysis.entity';
import { Suggestion } from '../entities/suggestion.entity';
import { TrackingTask } from '../entities/tracking-task.entity';
import { TrackingResult } from '../entities/tracking-result.entity';
import { Subscription } from '../entities/subscription.entity';
import { ContentEmbedding } from '../entities/content-embedding.entity';

@Injectable()
export class DatabaseConfig implements TypeOrmOptionsFactory {
  constructor(private configService: ConfigService) {}

  createTypeOrmOptions(): TypeOrmModuleOptions {
    const databaseUrl = this.configService.get<string>('DATABASE_URL');

    if (!databaseUrl) {
      throw new Error('DATABASE_URL is not configured in environment variables');
    }

    return {
      type: 'postgres',
      url: databaseUrl,
      entities: [
        User,
        Content,
        Analysis,
        Suggestion,
        TrackingTask,
        TrackingResult,
        Subscription,
        ContentEmbedding,
      ],
      synchronize: false,
      logging: true,
      ssl: {
        rejectUnauthorized: false,
      },
      retryAttempts: 3,
      retryDelay: 3000,
      autoLoadEntities: true,
    };
  }
}