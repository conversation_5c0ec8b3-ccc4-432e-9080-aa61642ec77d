import { IsString, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class GenerateKeywordsDto {
  @IsString({ message: '内容必须是字符串' })
  @MinLength(10, { message: '内容长度至少10字符' })
  content: string;

  @IsOptional()
  @IsString({ message: '目标受众必须是字符串' })
  targetAudience?: string;

  @IsOptional()
  @IsString({ message: '行业必须是字符串' })
  industry?: string;

  @IsOptional()
  @IsNumber({}, { message: '关键词数量必须是数字' })
  @Min(5, { message: '关键词数量至少5个' })
  @Max(50, { message: '关键词数量最多50个' })
  keywordCount?: number;
}