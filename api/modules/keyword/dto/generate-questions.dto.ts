import { IsString, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class GenerateQuestionsDto {
  @IsString({ message: '内容必须是字符串' })
  @MinLength(10, { message: '内容长度至少10字符' })
  content: string;

  @IsOptional()
  @IsString({ message: '问题类型必须是字符串' })
  @IsIn(['what', 'how', 'why', 'when', 'where', 'who', 'all'], {
    message: '问题类型必须是: what, how, why, when, where, who, all 之一',
  })
  questionType?: string;

  @IsOptional()
  @IsNumber({}, { message: '问题数量必须是数字' })
  @Min(5, { message: '问题数量至少5个' })
  @Max(30, { message: '问题数量最多30个' })
  questionCount?: number;

  @IsOptional()
  @IsString({ message: '目标受众必须是字符串' })
  targetAudience?: string;
}