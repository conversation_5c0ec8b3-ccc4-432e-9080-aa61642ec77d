import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GenerateKeywordsDto } from './dto/generate-keywords.dto';
import { GenerateQuestionsDto } from './dto/generate-questions.dto';
import OpenAI from 'openai';

@Injectable()
export class KeywordService {
  private openai: OpenAI;

  constructor(private configService: ConfigService) {
    this.openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });
  }

  async generateKeywords(generateKeywordsDto: GenerateKeywordsDto) {
    const { content, targetAudience, industry, keywordCount = 20 } = generateKeywordsDto;

    const prompt = `
作为一个SEO和GEO专家，请基于以下内容生成相关关键词：

内容：
${content}

目标受众：${targetAudience || '通用受众'}
行业：${industry || '通用行业'}

请生成${keywordCount}个关键词，包括：
1. 主要关键词（5-8个）- 直接相关的核心词汇
2. 长尾关键词（8-10个）- 更具体的短语
3. 语义相关词（5-7个）- 语义上相关的词汇

每个关键词请包含：
- keyword: 关键词文本
- type: 类型（primary/long-tail/semantic）
- searchVolume: 预估搜索量（high/medium/low）
- difficulty: 竞争难度（easy/medium/hard）
- relevance: 相关性评分（1-10）

请以JSON格式返回关键词数组。
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
      });

      const result = JSON.parse(response.choices[0].message.content || '[]');
      
      return {
        keywords: Array.isArray(result) ? result : [],
        totalCount: Array.isArray(result) ? result.length : 0,
        generatedAt: new Date(),
        recommendations: this.getKeywordRecommendations(),
      };
    } catch (error) {
      console.error('关键词生成失败:', error);
      
      // 返回默认关键词
      return {
        keywords: this.getDefaultKeywords(),
        totalCount: 10,
        generatedAt: new Date(),
        recommendations: this.getKeywordRecommendations(),
        error: '由于API限制，返回默认关键词',
      };
    }
  }

  async generateQuestions(generateQuestionsDto: GenerateQuestionsDto) {
    const { content, questionType = 'all', questionCount = 15 } = generateQuestionsDto;

    const questionTypes = {
      what: '什么是、什么叫、什么意思',
      how: '如何、怎么、怎样',
      why: '为什么、为何',
      when: '什么时候、何时',
      where: '在哪里、哪里',
      who: '谁、什么人',
      all: '所有类型',
    };

    const typeDescription = questionTypes[questionType] || questionTypes.all;

    const prompt = `
作为一个内容策略专家，请基于以下内容生成用户可能搜索的问题：

内容：
${content}

问题类型：${typeDescription}

请生成${questionCount}个问题，确保：
1. 问题自然且符合用户搜索习惯
2. 涵盖不同的搜索意图（信息性、导航性、交易性）
3. 包含不同难度级别的问题
4. 适合GEO优化

每个问题请包含：
- question: 问题文本
- type: 问题类型（what/how/why/when/where/who）
- intent: 搜索意图（informational/navigational/transactional）
- difficulty: 回答难度（easy/medium/hard）
- popularity: 预估受欢迎程度（high/medium/low）

请以JSON格式返回问题数组。
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.4,
      });

      const result = JSON.parse(response.choices[0].message.content || '[]');
      
      return {
        questions: Array.isArray(result) ? result : [],
        totalCount: Array.isArray(result) ? result.length : 0,
        questionType,
        generatedAt: new Date(),
        recommendations: this.getQuestionRecommendations(),
      };
    } catch (error) {
      console.error('问题生成失败:', error);
      
      // 返回默认问题
      return {
        questions: this.getDefaultQuestions(),
        totalCount: 10,
        questionType,
        generatedAt: new Date(),
        recommendations: this.getQuestionRecommendations(),
        error: '由于API限制，返回默认问题',
      };
    }
  }

  async analyzeKeywordTrends(keywords: string[]) {
    // 模拟关键词趋势分析
    const trends = keywords.map(keyword => ({
      keyword,
      trend: Math.random() > 0.5 ? 'rising' : 'stable',
      changePercent: Math.floor(Math.random() * 50) - 25,
      seasonality: Math.random() > 0.7 ? 'seasonal' : 'stable',
    }));

    return {
      trends,
      summary: {
        risingKeywords: trends.filter(t => t.trend === 'rising').length,
        stableKeywords: trends.filter(t => t.trend === 'stable').length,
        seasonalKeywords: trends.filter(t => t.seasonality === 'seasonal').length,
      },
    };
  }

  private getDefaultKeywords() {
    return [
      {
        keyword: '内容优化',
        type: 'primary',
        searchVolume: 'medium',
        difficulty: 'medium',
        relevance: 8,
      },
      {
        keyword: 'SEO优化技巧',
        type: 'long-tail',
        searchVolume: 'medium',
        difficulty: 'easy',
        relevance: 9,
      },
      {
        keyword: '搜索引擎优化',
        type: 'primary',
        searchVolume: 'high',
        difficulty: 'hard',
        relevance: 10,
      },
      {
        keyword: '内容营销策略',
        type: 'semantic',
        searchVolume: 'medium',
        difficulty: 'medium',
        relevance: 7,
      },
      {
        keyword: '如何提高网站排名',
        type: 'long-tail',
        searchVolume: 'low',
        difficulty: 'easy',
        relevance: 8,
      },
    ];
  }

  private getDefaultQuestions() {
    return [
      {
        question: '什么是SEO优化？',
        type: 'what',
        intent: 'informational',
        difficulty: 'easy',
        popularity: 'high',
      },
      {
        question: '如何提高网站在搜索引擎中的排名？',
        type: 'how',
        intent: 'informational',
        difficulty: 'medium',
        popularity: 'high',
      },
      {
        question: '为什么需要进行内容优化？',
        type: 'why',
        intent: 'informational',
        difficulty: 'easy',
        popularity: 'medium',
      },
      {
        question: '什么时候应该更新网站内容？',
        type: 'when',
        intent: 'informational',
        difficulty: 'medium',
        popularity: 'medium',
      },
      {
        question: '在哪里可以学习SEO技巧？',
        type: 'where',
        intent: 'navigational',
        difficulty: 'easy',
        popularity: 'medium',
      },
    ];
  }

  private getKeywordRecommendations(): string[] {
    return [
      '优先使用长尾关键词，竞争较小且转化率更高',
      '关注语义相关词汇，提高内容的相关性',
      '定期监控关键词表现，及时调整策略',
      '结合用户搜索意图选择合适的关键词',
      '避免关键词堆砌，保持内容自然性',
    ];
  }

  private getQuestionRecommendations(): string[] {
    return [
      '创建FAQ页面回答常见问题',
      '在内容中自然地回答这些问题',
      '使用问题作为文章标题或小标题',
      '关注"People Also Ask"类型的问题',
      '定期更新问题库以反映最新趋势',
    ];
  }
}