import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { KeywordService } from './keyword.service';
import { GenerateKeywordsDto } from './dto/generate-keywords.dto';
import { GenerateQuestionsDto } from './dto/generate-questions.dto';
import { AnalyzeKeywordTrendsDto } from './dto/analyze-keyword-trends.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('keyword')
@UseGuards(JwtAuthGuard)
export class KeywordController {
  constructor(private keywordService: KeywordService) {}

  @Post('generate')
  async generateKeywords(@Body() generateKeywordsDto: GenerateKeywordsDto) {
    const result = await this.keywordService.generateKeywords(generateKeywordsDto);
    return {
      success: true,
      ...result,
    };
  }

  @Post('questions/generate')
  async generateQuestions(@Body() generateQuestionsDto: GenerateQuestionsDto) {
    const result = await this.keywordService.generateQuestions(generateQuestionsDto);
    return {
      success: true,
      ...result,
    };
  }

  @Post('trends/analyze')
  async analyzeKeywordTrends(@Body() analyzeKeywordTrendsDto: AnalyzeKeywordTrendsDto) {
    const result = await this.keywordService.analyzeKeywordTrends(analyzeKeywordTrendsDto.keywords);
    return {
      success: true,
      ...result,
    };
  }
}