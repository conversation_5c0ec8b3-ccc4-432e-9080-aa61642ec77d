import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../entities/user.entity';
import { UpdateUserDto } from './dto/update-user.dto';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async findById(id: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }
    return user;
  }

  async updateUser(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findById(id);
    
    Object.assign(user, updateUserDto);
    return this.userRepository.save(user);
  }

  async incrementUsageCount(id: string): Promise<void> {
    await this.userRepository.increment({ id }, 'usageCount', 1);
  }

  async getUserStats(id: string) {
    const user = await this.findById(id);
    
    return {
      usageCount: user.usageCount,
      plan: user.plan,
      createdAt: user.createdAt,
    };
  }
}