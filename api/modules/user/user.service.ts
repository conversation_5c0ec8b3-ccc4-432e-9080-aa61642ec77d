import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../entities/user.entity';
import { UpdateUserDto } from './dto/update-user.dto';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async findById(id: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('用户不存在');
    }
    return user;
  }

  async updateUser(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findById(id);
    
    Object.assign(user, updateUserDto);
    return this.userRepository.save(user);
  }

  async incrementUsageCount(id: string): Promise<void> {
    // Since we don't have a usage_count column, this is a no-op for now
    // In the future, we could track usage in a separate table or calculate from contents
    console.log(`Usage increment requested for user ${id}`);
  }

  async getUserStats(id: string) {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['contents']
    });

    if (!user) {
      throw new Error('User not found');
    }

    return {
      usageCount: user.usageCount, // This will use the getter we defined
      plan: user.plan,
      createdAt: user.createdAt,
    };
  }
}