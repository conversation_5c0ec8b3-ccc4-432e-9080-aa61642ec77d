import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { TrackingTask, TrackingStatus } from '../../entities/tracking-task.entity';
import { TrackingResult } from '../../entities/tracking-result.entity';
import { CreateTrackingTaskDto } from './dto/create-tracking-task.dto';
import OpenAI from 'openai';

@Injectable()
export class TrackingService {
  private openai: OpenAI;

  constructor(
    @InjectRepository(TrackingTask)
    private trackingTaskRepository: Repository<TrackingTask>,
    @InjectRepository(TrackingResult)
    private trackingResultRepository: Repository<TrackingResult>,
    private configService: ConfigService,
  ) {
    this.openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });
  }

  async createTrackingTask(createTrackingTaskDto: CreateTrackingTaskDto): Promise<TrackingTask> {
    const task = this.trackingTaskRepository.create({
      ...createTrackingTaskDto,
      platforms: JSON.stringify(createTrackingTaskDto.platforms),
      queries: JSON.stringify(createTrackingTaskDto.queries),
    });
    const savedTask = await this.trackingTaskRepository.save(task);

    // 异步执行追踪任务
    this.executeTrackingTask(savedTask.id).catch(error => {
      console.error('追踪任务执行失败:', error);
    });

    return savedTask;
  }

  async getTrackingTasks(contentId: string): Promise<TrackingTask[]> {
    return this.trackingTaskRepository.find({
      where: { contentId },
      order: { createdAt: 'DESC' },
      relations: ['results'],
    });
  }

  async getTrackingTask(id: string): Promise<TrackingTask> {
    const task = await this.trackingTaskRepository.findOne({
      where: { id },
      relations: ['results'],
    });

    if (!task) {
      throw new NotFoundException('追踪任务不存在');
    }

    return task;
  }

  async getTrackingResults(taskId: string): Promise<TrackingResult[]> {
    return this.trackingResultRepository.find({
      where: { taskId },
      order: { checkedAt: 'DESC' },
    });
  }

  private async executeTrackingTask(taskId: string): Promise<void> {
    const task = await this.trackingTaskRepository.findOne({
      where: { id: taskId },
      relations: ['content'],
    });

    if (!task) {
      return;
    }

    try {
      // 更新任务状态为进行中
      task.status = TrackingStatus.RUNNING;
      await this.trackingTaskRepository.save(task);

      const results: TrackingResult[] = [];

      // 对每个平台和查询进行追踪
      const platforms = JSON.parse(task.platforms);
      const queries = JSON.parse(task.queries);
      
      for (const platform of platforms) {
        for (const query of queries) {
          const result = await this.trackContentOnPlatform(
            platform,
            query,
            task.content.originalContent,
          );

          const trackingResult = this.trackingResultRepository.create({
            taskId: task.id,
            platform,
            query,
            foundCitation: result.foundCitation,
            aiResponse: result.aiResponse,
            similarityScore: result.similarityScore,
          });

          const savedResult = await this.trackingResultRepository.save(trackingResult);
          results.push(savedResult);
        }
      }

      // 更新任务状态为完成
      task.status = TrackingStatus.COMPLETED;
      await this.trackingTaskRepository.save(task);

    } catch (error) {
      console.error('追踪任务执行失败:', error);
      
      // 更新任务状态为失败
      task.status = TrackingStatus.FAILED;
      await this.trackingTaskRepository.save(task);
    }
  }

  private async trackContentOnPlatform(
    platform: string,
    query: string,
    originalContent: string,
  ): Promise<{
    foundCitation: boolean;
    aiResponse: string;
    similarityScore: number;
  }> {
    // 模拟AI平台搜索和内容比较
    const prompt = `
作为一个内容追踪专家，请模拟在${platform}平台上搜索"${query}"的结果，并分析是否引用了以下原始内容：

原始内容：
${originalContent}

请返回JSON格式的结果，包含：
- foundCitation: 是否找到引用（boolean）
- aiResponse: 模拟的AI回答
- similarityScore: 相似度评分（0-1）
- citationContext: 引用上下文（如果找到）
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
      });

      const result = JSON.parse(response.choices[0].message.content || '{}');
      
      return {
        foundCitation: result.foundCitation || false,
        aiResponse: result.aiResponse || '未找到相关回答',
        similarityScore: result.similarityScore || 0,
      };
    } catch (error) {
      console.error('内容追踪失败:', error);
      
      // 返回默认结果
      return {
        foundCitation: Math.random() > 0.7, // 30%概率找到引用
        aiResponse: `在${platform}上搜索"${query}"的模拟结果`,
        similarityScore: Math.random() * 0.5 + 0.3, // 0.3-0.8的随机相似度
      };
    }
  }

  async getTrackingStats(contentId?: string) {
    const whereCondition = contentId ? { contentId } : {};
    
    const totalTasks = await this.trackingTaskRepository.count({ where: whereCondition });
    const completedTasks = await this.trackingTaskRepository.count({
      where: { ...whereCondition, status: TrackingStatus.COMPLETED },
    });
    const inProgressTasks = await this.trackingTaskRepository.count({
      where: { ...whereCondition, status: TrackingStatus.RUNNING },
    });
    
    const totalResults = await this.trackingResultRepository.count({
      where: contentId ? { task: { contentId } } : {},
      relations: contentId ? ['task'] : [],
    });
    
    const foundCitations = await this.trackingResultRepository.count({
      where: {
        foundCitation: true,
        ...(contentId ? { task: { contentId } } : {}),
      },
      relations: contentId ? ['task'] : [],
    });

    return {
      totalTasks,
      completedTasks,
      inProgressTasks,
      totalResults,
      foundCitations,
      citationRate: totalResults > 0 ? (foundCitations / totalResults) * 100 : 0,
    };
  }

  async retryTrackingTask(taskId: string): Promise<TrackingTask> {
    const task = await this.getTrackingTask(taskId);
    
    // 重置任务状态
    task.status = TrackingStatus.PENDING;
    const updatedTask = await this.trackingTaskRepository.save(task);

    // 删除之前的结果
    await this.trackingResultRepository.delete({ taskId });

    // 重新执行追踪任务
    this.executeTrackingTask(taskId).catch(error => {
      console.error('重试追踪任务失败:', error);
    });

    return updatedTask;
  }
}