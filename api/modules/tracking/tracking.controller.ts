import {
  Controller,
  Get,
  Post,
  Param,
  Body,
  UseGuards,
  Query,
} from '@nestjs/common';
import { TrackingService } from './tracking.service';
import { CreateTrackingTaskDto } from './dto/create-tracking-task.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('tracking')
@UseGuards(JwtAuthGuard)
export class TrackingController {
  constructor(private trackingService: TrackingService) {}

  @Post('tasks')
  async createTrackingTask(@Body() createTrackingTaskDto: CreateTrackingTaskDto) {
    const task = await this.trackingService.createTrackingTask(createTrackingTaskDto);
    return {
      success: true,
      task,
    };
  }

  @Get('tasks')
  async getTrackingTasks(@Query('contentId') contentId: string) {
    const tasks = await this.trackingService.getTrackingTasks(contentId);
    return {
      success: true,
      tasks,
    };
  }

  @Get('tasks/:id')
  async getTrackingTask(@Param('id') id: string) {
    const task = await this.trackingService.getTrackingTask(id);
    return {
      success: true,
      task,
    };
  }

  @Get('tasks/:id/results')
  async getTrackingResults(@Param('id') taskId: string) {
    const results = await this.trackingService.getTrackingResults(taskId);
    return {
      success: true,
      results,
    };
  }

  @Post('tasks/:id/retry')
  async retryTrackingTask(@Param('id') taskId: string) {
    const task = await this.trackingService.retryTrackingTask(taskId);
    return {
      success: true,
      task,
    };
  }

  @Get('stats')
  async getTrackingStats(@Query('contentId') contentId?: string) {
    const stats = await this.trackingService.getTrackingStats(contentId);
    return {
      success: true,
      stats,
    };
  }
}