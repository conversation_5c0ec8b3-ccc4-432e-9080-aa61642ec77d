import { IsString, IsArray, ArrayMinSize, ArrayMaxSize } from 'class-validator';

export class CreateTrackingTaskDto {
  @IsString({ message: '内容ID必须是字符串' })
  contentId: string;

  @IsArray({ message: '平台列表必须是数组' })
  @IsString({ each: true, message: '每个平台必须是字符串' })
  @ArrayMinSize(1, { message: '至少需要选择1个平台' })
  @ArrayMaxSize(10, { message: '最多支持10个平台' })
  platforms: string[];

  @IsArray({ message: '查询列表必须是数组' })
  @IsString({ each: true, message: '每个查询必须是字符串' })
  @ArrayMinSize(1, { message: '至少需要1个查询' })
  @ArrayMaxSize(20, { message: '最多支持20个查询' })
  queries: string[];
}