import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RewriteContentDto } from './dto/rewrite-content.dto';
import { OptimizeContentDto } from './dto/optimize-content.dto';
import { ImproveReadabilityDto } from './dto/improve-readability.dto';
import OpenAI from 'openai';

export interface RewriteResult {
  originalContent: string;
  rewrittenContent: string;
  improvements: {
    readabilityScore: number;
    geoScore: number;
    structureScore: number;
    engagementScore: number;
  };
  changes: {
    type: 'structure' | 'clarity' | 'engagement' | 'geo' | 'readability';
    description: string;
    impact: 'high' | 'medium' | 'low';
  }[];
  suggestions: string[];
  wordCount: {
    original: number;
    rewritten: number;
    change: number;
  };
  generatedAt: string;
  error?: string;
}

export interface OptimizeResult {
  originalContent: string;
  optimizedContent: string;
  optimizations: {
    seoScore: number;
    keywordDensity: number;
    readabilityScore: number;
    structureScore: number;
  };
  appliedOptimizations: {
    type: 'keyword' | 'structure' | 'meta' | 'readability';
    description: string;
    value: string;
  }[];
  recommendations: string[];
  generatedAt: string;
  error?: string;
}

export interface ReadabilityResult {
  originalContent: string;
  improvedContent: string;
  readabilityMetrics: {
    fleschScore: number;
    averageSentenceLength: number;
    averageWordsPerSentence: number;
    complexWordsPercentage: number;
  };
  improvements: {
    type: 'sentence' | 'vocabulary' | 'structure' | 'flow';
    description: string;
    before: string;
    after: string;
  }[];
  suggestions: string[];
  generatedAt: string;
  error?: string;
}

@Injectable()
export class RewriteService {
  private openai: OpenAI;

  constructor(private configService: ConfigService) {
    this.openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });
  }

  async rewriteContent(rewriteContentDto: RewriteContentDto): Promise<RewriteResult> {
    const { content, style = 'professional', tone = 'neutral', targetAudience, preserveLength = false } = rewriteContentDto;

    try {
      const prompt = this.buildRewritePrompt(content, style, tone, targetAudience, preserveLength);
      
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的内容重构专家，专注于GEO优化和内容质量提升。请按照要求重构内容，并提供详细的改进分析。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000,
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('AI响应为空');
      }

      return this.parseRewriteResponse(response, content);
    } catch (error) {
      console.error('内容重构失败:', error);
      return this.getDefaultRewriteResult(content);
    }
  }

  async optimizeContent(optimizeContentDto: OptimizeContentDto): Promise<OptimizeResult> {
    const { content, keywords = [], targetSEO = true, focusArea = 'overall' } = optimizeContentDto;

    try {
      const prompt = this.buildOptimizePrompt(content, keywords, targetSEO, focusArea);
      
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: '你是一个SEO和GEO优化专家，专注于内容优化以提升搜索引擎表现和AI引用价值。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.6,
        max_tokens: 2000,
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('AI响应为空');
      }

      return this.parseOptimizeResponse(response, content);
    } catch (error) {
      console.error('内容优化失败:', error);
      return this.getDefaultOptimizeResult(content);
    }
  }

  async improveReadability(improveReadabilityDto: ImproveReadabilityDto): Promise<ReadabilityResult> {
    const { content, targetLevel = 'intermediate', simplifyLanguage = true, improveSentenceStructure = true } = improveReadabilityDto;

    try {
      const prompt = this.buildReadabilityPrompt(content, targetLevel, simplifyLanguage, improveSentenceStructure);
      
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的文本可读性优化专家，专注于提升内容的易读性和理解性。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.5,
        max_tokens: 2000,
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('AI响应为空');
      }

      return this.parseReadabilityResponse(response, content);
    } catch (error) {
      console.error('可读性优化失败:', error);
      return this.getDefaultReadabilityResult(content);
    }
  }

  private buildRewritePrompt(content: string, style: string, tone: string, targetAudience?: string, preserveLength?: boolean): string {
    return `
请重构以下内容，要求：

内容：
${content}

重构要求：
- 风格：${style}
- 语调：${tone}
${targetAudience ? `- 目标受众：${targetAudience}` : ''}
${preserveLength ? '- 保持原文长度' : '- 可适当调整长度以提升质量'}

请提供以下格式的JSON响应：
{
  "rewrittenContent": "重构后的内容",
  "improvements": {
    "readabilityScore": 85,
    "geoScore": 90,
    "structureScore": 88,
    "engagementScore": 82
  },
  "changes": [
    {
      "type": "structure",
      "description": "改进了段落结构",
      "impact": "high"
    }
  ],
  "suggestions": ["建议1", "建议2"]
}
`;
  }

  private buildOptimizePrompt(content: string, keywords: string[], targetSEO: boolean, focusArea: string): string {
    return `
请优化以下内容以提升SEO和GEO表现：

内容：
${content}

优化参数：
- 目标关键词：${keywords.join(', ')}
- SEO优化：${targetSEO ? '是' : '否'}
- 重点领域：${focusArea}

请提供以下格式的JSON响应：
{
  "optimizedContent": "优化后的内容",
  "optimizations": {
    "seoScore": 88,
    "keywordDensity": 2.5,
    "readabilityScore": 85,
    "structureScore": 90
  },
  "appliedOptimizations": [
    {
      "type": "keyword",
      "description": "优化关键词密度",
      "value": "2.5%"
    }
  ],
  "recommendations": ["建议1", "建议2"]
}
`;
  }

  private buildReadabilityPrompt(content: string, targetLevel: string, simplifyLanguage: boolean, improveSentenceStructure: boolean): string {
    return `
请优化以下内容的可读性：

内容：
${content}

优化要求：
- 目标阅读水平：${targetLevel}
- 简化语言：${simplifyLanguage ? '是' : '否'}
- 改进句子结构：${improveSentenceStructure ? '是' : '否'}

请提供以下格式的JSON响应：
{
  "improvedContent": "优化后的内容",
  "readabilityMetrics": {
    "fleschScore": 75,
    "averageSentenceLength": 15,
    "averageWordsPerSentence": 12,
    "complexWordsPercentage": 8
  },
  "improvements": [
    {
      "type": "sentence",
      "description": "简化了复杂句子",
      "before": "原句子",
      "after": "改进后句子"
    }
  ],
  "suggestions": ["建议1", "建议2"]
}
`;
  }

  private parseRewriteResponse(response: string, originalContent: string): RewriteResult {
    try {
      const parsed = JSON.parse(response);
      return {
        originalContent,
        rewrittenContent: parsed.rewrittenContent || originalContent,
        improvements: parsed.improvements || {
          readabilityScore: 75,
          geoScore: 80,
          structureScore: 78,
          engagementScore: 76
        },
        changes: parsed.changes || [],
        suggestions: parsed.suggestions || [],
        wordCount: {
          original: originalContent.split(' ').length,
          rewritten: (parsed.rewrittenContent || originalContent).split(' ').length,
          change: (parsed.rewrittenContent || originalContent).split(' ').length - originalContent.split(' ').length
        },
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      return this.getDefaultRewriteResult(originalContent);
    }
  }

  private parseOptimizeResponse(response: string, originalContent: string): OptimizeResult {
    try {
      const parsed = JSON.parse(response);
      return {
        originalContent,
        optimizedContent: parsed.optimizedContent || originalContent,
        optimizations: parsed.optimizations || {
          seoScore: 75,
          keywordDensity: 2.0,
          readabilityScore: 80,
          structureScore: 78
        },
        appliedOptimizations: parsed.appliedOptimizations || [],
        recommendations: parsed.recommendations || [],
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      return this.getDefaultOptimizeResult(originalContent);
    }
  }

  private parseReadabilityResponse(response: string, originalContent: string): ReadabilityResult {
    try {
      const parsed = JSON.parse(response);
      return {
        originalContent,
        improvedContent: parsed.improvedContent || originalContent,
        readabilityMetrics: parsed.readabilityMetrics || {
          fleschScore: 70,
          averageSentenceLength: 18,
          averageWordsPerSentence: 15,
          complexWordsPercentage: 12
        },
        improvements: parsed.improvements || [],
        suggestions: parsed.suggestions || [],
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      return this.getDefaultReadabilityResult(originalContent);
    }
  }

  private getDefaultRewriteResult(content: string): RewriteResult {
    return {
      originalContent: content,
      rewrittenContent: content,
      improvements: {
        readabilityScore: 70,
        geoScore: 75,
        structureScore: 72,
        engagementScore: 68
      },
      changes: [
        {
          type: 'structure',
          description: '由于API限制，返回默认结果',
          impact: 'low'
        }
      ],
      suggestions: [
        '建议手动检查内容结构',
        '考虑优化段落长度',
        '增强内容的吸引力'
      ],
      wordCount: {
        original: content.split(' ').length,
        rewritten: content.split(' ').length,
        change: 0
      },
      generatedAt: new Date().toISOString(),
      error: '由于API限制，返回默认重构结果'
    };
  }

  private getDefaultOptimizeResult(content: string): OptimizeResult {
    return {
      originalContent: content,
      optimizedContent: content,
      optimizations: {
        seoScore: 70,
        keywordDensity: 1.8,
        readabilityScore: 75,
        structureScore: 72
      },
      appliedOptimizations: [
        {
          type: 'structure',
          description: '默认优化建议',
          value: '基础结构'
        }
      ],
      recommendations: [
        '增加相关关键词',
        '优化标题结构',
        '改进内容层次'
      ],
      generatedAt: new Date().toISOString(),
      error: '由于API限制，返回默认优化结果'
    };
  }

  private getDefaultReadabilityResult(content: string): ReadabilityResult {
    return {
      originalContent: content,
      improvedContent: content,
      readabilityMetrics: {
        fleschScore: 65,
        averageSentenceLength: 20,
        averageWordsPerSentence: 16,
        complexWordsPercentage: 15
      },
      improvements: [
        {
          type: 'sentence',
          description: '默认改进建议',
          before: '复杂句子示例',
          after: '简化句子示例'
        }
      ],
      suggestions: [
        '简化复杂句子',
        '减少专业术语',
        '增加过渡词'
      ],
      generatedAt: new Date().toISOString(),
      error: '由于API限制，返回默认可读性结果'
    };
  }
}