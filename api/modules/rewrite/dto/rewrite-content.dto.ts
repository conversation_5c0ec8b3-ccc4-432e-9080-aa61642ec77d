import { IsString, IsOptional, IsBoolean, IsIn } from 'class-validator';

export class RewriteContentDto {
  @IsString()
  content: string;

  @IsOptional()
  @IsIn(['professional', 'casual', 'academic', 'creative', 'technical'])
  style?: string;

  @IsOptional()
  @IsIn(['neutral', 'friendly', 'formal', 'persuasive', 'informative'])
  tone?: string;

  @IsOptional()
  @IsString()
  targetAudience?: string;

  @IsOptional()
  @IsBoolean()
  preserveLength?: boolean;
}