import { IsString, IsOptional, IsBoolean, IsA<PERSON>y, IsIn } from 'class-validator';

export class OptimizeContentDto {
  @IsString()
  content: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keywords?: string[];

  @IsOptional()
  @IsBoolean()
  targetSEO?: boolean;

  @IsOptional()
  @IsIn(['overall', 'keywords', 'structure', 'readability', 'engagement'])
  focusArea?: string;
}