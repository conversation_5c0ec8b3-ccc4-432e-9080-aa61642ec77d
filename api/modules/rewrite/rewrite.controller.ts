import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { RewriteService } from './rewrite.service';
import { RewriteContentDto } from './dto/rewrite-content.dto';
import { OptimizeContentDto } from './dto/optimize-content.dto';
import { ImproveReadabilityDto } from './dto/improve-readability.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('rewrite')
@UseGuards(JwtAuthGuard)
export class RewriteController {
  constructor(private readonly rewriteService: RewriteService) {}

  @Post('content')
  async rewriteContent(@Body() rewriteContentDto: RewriteContentDto) {
    const result = await this.rewriteService.rewriteContent(rewriteContentDto);
    return {
      success: true,
      data: result,
    };
  }

  @Post('optimize')
  async optimizeContent(@Body() optimizeContentDto: OptimizeContentDto) {
    const result = await this.rewriteService.optimizeContent(optimizeContentDto);
    return {
      success: true,
      data: result,
    };
  }

  @Post('readability')
  async improveReadability(@Body() improveReadabilityDto: ImproveReadabilityDto) {
    const result = await this.rewriteService.improveReadability(improveReadabilityDto);
    return {
      success: true,
      data: result,
    };
  }
}