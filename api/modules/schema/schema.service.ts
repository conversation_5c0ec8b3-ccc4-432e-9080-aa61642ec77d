import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GenerateSchemaDto } from './dto/generate-schema.dto';
import OpenAI from 'openai';

@Injectable()
export class SchemaService {
  private openai: OpenAI;

  constructor(private configService: ConfigService) {
    this.openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });
  }

  async generateSchema(generateSchemaDto: GenerateSchemaDto) {
    const { content, schemaType, targetAudience } = generateSchemaDto;

    const schemaTemplates = {
      article: this.getArticleSchemaTemplate(),
      faq: this.getFAQSchemaTemplate(),
      howto: this.getHowToSchemaTemplate(),
      product: this.getProductSchemaTemplate(),
      organization: this.getOrganizationSchemaTemplate(),
    };

    const template = schemaTemplates[schemaType] || schemaTemplates.article;

    const prompt = `
作为一个结构化数据专家，请根据以下内容生成${schemaType}类型的JSON-LD结构化标注：

内容：
${content}

目标受众：${targetAudience || '通用受众'}

请生成符合Schema.org标准的JSON-LD格式，确保：
1. 包含所有必要的属性
2. 数据准确且相关
3. 符合搜索引擎最佳实践
4. 针对GEO优化

模板参考：
${JSON.stringify(template, null, 2)}

请返回完整的JSON-LD结构化数据。
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.2,
      });

      const generatedSchema = response.choices[0].message.content;
      
      // 尝试解析JSON以验证格式
      let parsedSchema;
      try {
        parsedSchema = JSON.parse(generatedSchema || '{}');
      } catch {
        // 如果解析失败，返回默认模板
        parsedSchema = template;
      }

      return {
        schema: parsedSchema,
        schemaType,
        generatedAt: new Date(),
        recommendations: this.getSchemaRecommendations(schemaType),
      };
    } catch (error) {
      console.error('Schema生成失败:', error);
      
      // 返回默认模板
      return {
        schema: template,
        schemaType,
        generatedAt: new Date(),
        recommendations: this.getSchemaRecommendations(schemaType),
        error: '由于API限制，返回默认模板',
      };
    }
  }

  private getArticleSchemaTemplate() {
    return {
      '@context': 'https://schema.org',
      '@type': 'Article',
      headline: '文章标题',
      description: '文章描述',
      author: {
        '@type': 'Person',
        name: '作者姓名',
      },
      publisher: {
        '@type': 'Organization',
        name: '发布机构',
        logo: {
          '@type': 'ImageObject',
          url: 'https://example.com/logo.png',
        },
      },
      datePublished: new Date().toISOString(),
      dateModified: new Date().toISOString(),
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': 'https://example.com/article',
      },
    };
  }

  private getFAQSchemaTemplate() {
    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: [
        {
          '@type': 'Question',
          name: '问题1',
          acceptedAnswer: {
            '@type': 'Answer',
            text: '答案1',
          },
        },
      ],
    };
  }

  private getHowToSchemaTemplate() {
    return {
      '@context': 'https://schema.org',
      '@type': 'HowTo',
      name: '如何做某事',
      description: '详细描述',
      step: [
        {
          '@type': 'HowToStep',
          name: '步骤1',
          text: '步骤描述',
        },
      ],
    };
  }

  private getProductSchemaTemplate() {
    return {
      '@context': 'https://schema.org',
      '@type': 'Product',
      name: '产品名称',
      description: '产品描述',
      brand: {
        '@type': 'Brand',
        name: '品牌名称',
      },
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'CNY',
        availability: 'https://schema.org/InStock',
      },
    };
  }

  private getOrganizationSchemaTemplate() {
    return {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: '组织名称',
      description: '组织描述',
      url: 'https://example.com',
      logo: 'https://example.com/logo.png',
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: '+86-xxx-xxxx',
        contactType: 'customer service',
      },
    };
  }

  private getSchemaRecommendations(schemaType: string): string[] {
    const recommendations = {
      article: [
        '确保包含准确的发布日期和修改日期',
        '添加作者信息以提高可信度',
        '包含高质量的特色图片',
        '使用清晰的文章标题和描述',
      ],
      faq: [
        '确保问题和答案简洁明了',
        '使用用户常问的问题',
        '答案要准确且有帮助',
        '考虑添加相关的内部链接',
      ],
      howto: [
        '步骤要清晰且按逻辑顺序排列',
        '包含所需的工具和材料',
        '添加预计完成时间',
        '考虑添加图片或视频说明',
      ],
      product: [
        '包含准确的价格和可用性信息',
        '添加产品评价和评分',
        '包含详细的产品规格',
        '确保品牌信息准确',
      ],
      organization: [
        '包含完整的联系信息',
        '添加社交媒体链接',
        '确保地址信息准确',
        '包含营业时间信息',
      ],
    };

    return recommendations[schemaType] || recommendations.article;
  }

  async validateSchema(schema: any): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    // 基本验证
    if (!schema['@context']) {
      errors.push('缺少@context属性');
    }

    if (!schema['@type']) {
      errors.push('缺少@type属性');
    }

    // 根据类型进行特定验证
    if (schema['@type'] === 'Article') {
      if (!schema.headline) errors.push('Article类型缺少headline属性');
      if (!schema.author) errors.push('Article类型缺少author属性');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}