import { IsString, IsOptional, IsIn, MinLength } from 'class-validator';

export class GenerateSchemaDto {
  @IsString({ message: '内容必须是字符串' })
  @MinLength(10, { message: '内容长度至少10字符' })
  content: string;

  @IsString({ message: 'Schema类型必须是字符串' })
  @IsIn(['article', 'faq', 'howto', 'product', 'organization'], {
    message: 'Schema类型必须是: article, faq, howto, product, organization 之一',
  })
  schemaType: string;

  @IsOptional()
  @IsString({ message: '目标受众必须是字符串' })
  targetAudience?: string;

  @IsOptional()
  @IsString({ message: '网站URL必须是字符串' })
  websiteUrl?: string;
}