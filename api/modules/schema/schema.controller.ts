import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { SchemaService } from './schema.service';
import { GenerateSchemaDto } from './dto/generate-schema.dto';
import { ValidateSchemaDto } from './dto/validate-schema.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('schema')
@UseGuards(JwtAuthGuard)
export class SchemaController {
  constructor(private schemaService: SchemaService) {}

  @Post('generate')
  async generateSchema(@Body() generateSchemaDto: GenerateSchemaDto) {
    const result = await this.schemaService.generateSchema(generateSchemaDto);
    return {
      success: true,
      ...result,
    };
  }

  @Post('validate')
  async validateSchema(@Body() validateSchemaDto: ValidateSchemaDto) {
    const result = await this.schemaService.validateSchema(validateSchemaDto.schema);
    return {
      success: true,
      validation: result,
    };
  }
}