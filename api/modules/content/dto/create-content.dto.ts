import { IsString, IsOptional, IsUrl, IsObject, <PERSON><PERSON>ength, <PERSON><PERSON>ength } from 'class-validator';

export class CreateContentDto {
  @IsString({ message: '标题必须是字符串' })
  @MinLength(1, { message: '标题不能为空' })
  @MaxLength(200, { message: '标题长度不能超过200字符' })
  title: string;

  @IsString({ message: '原始内容必须是字符串' })
  @MinLength(10, { message: '内容长度至少10字符' })
  originalContent: string;

  @IsOptional()
  @IsString({ message: '内容类型必须是字符串' })
  contentType?: string;

  @IsOptional()
  @IsUrl({}, { message: '来源URL格式不正确' })
  sourceUrl?: string;

  @IsOptional()
  @IsObject({ message: '元数据必须是对象' })
  metadata?: Record<string, any>;
}