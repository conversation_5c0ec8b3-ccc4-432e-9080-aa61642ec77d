import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Analysis } from '../../entities/analysis.entity';
import { Suggestion } from '../../entities/suggestion.entity';
import { Content } from '../../entities/content.entity';
import { AnalyzeContentDto } from './dto/analyze-content.dto';
import OpenAI from 'openai';

@Injectable()
export class AnalysisService {
  private openai: OpenAI;

  constructor(
    @InjectRepository(Analysis)
    private analysisRepository: Repository<Analysis>,
    @InjectRepository(Suggestion)
    private suggestionRepository: Repository<Suggestion>,
    @InjectRepository(Content)
    private contentRepository: Repository<Content>,
    private configService: ConfigService,
  ) {
    this.openai = new OpenAI({
      apiKey: this.configService.get<string>('OPENAI_API_KEY'),
    });
  }

  async analyzeContent(contentId: string, analyzeDto: AnalyzeContentDto) {
    const content = await this.contentRepository.findOne({ where: { id: contentId } });
    if (!content) {
      throw new Error('内容不存在');
    }

    // 执行GEO分析
    const analysisResult = await this.performGEOAnalysis(content.originalContent);
    
    // 保存分析结果
    const analysis = this.analysisRepository.create({
      contentId,
      scores: analysisResult.scores,
      analysisResult: analysisResult.summary,
      modelVersion: 'gpt-4o',
    });

    const savedAnalysis = await this.analysisRepository.save(analysis);

    // 保存优化建议
    const suggestions = await Promise.all(
      analysisResult.suggestions.map(async (suggestion) => {
        const suggestionEntity = this.suggestionRepository.create({
          analysisId: savedAnalysis.id,
          suggestionType: suggestion.type,
          originalText: suggestion.originalText,
          suggestedText: suggestion.suggestedText,
          confidenceScore: suggestion.confidence,
        });
        return this.suggestionRepository.save(suggestionEntity);
      })
    );

    return {
      analysis: savedAnalysis,
      suggestions,
      scores: analysisResult.scores,
    };
  }

  private async performGEOAnalysis(content: string) {
    const prompt = `
作为一个GEO（生成式搜索引擎优化）专家，请分析以下内容并提供优化建议：

内容：
${content}

请从以下维度进行分析并给出0-100的评分：
1. 结构清晰度 - 内容是否有清晰的层次结构
2. 引用价值 - 内容是否容易被AI引用
3. 语义准确性 - 表达是否准确、无歧义
4. 信息密度 - 信息量是否丰富且有价值
5. 可读性 - 是否易于理解和阅读

请以JSON格式返回分析结果，包含：
- scores: 各维度评分对象
- summary: 总体分析摘要
- suggestions: 优化建议数组，每个建议包含type、originalText、suggestedText、confidence
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3,
      });

      const result = JSON.parse(response.choices[0].message.content || '{}');
      
      return {
        scores: result.scores || {
          structureClarity: 70,
          citationValue: 65,
          semanticAccuracy: 75,
          informationDensity: 70,
          readability: 80,
        },
        summary: result.summary || '内容分析完成',
        suggestions: result.suggestions || [],
      };
    } catch (error) {
      console.error('OpenAI API调用失败:', error);
      
      // 返回默认分析结果
      return {
        scores: {
          structureClarity: 70,
          citationValue: 65,
          semanticAccuracy: 75,
          informationDensity: 70,
          readability: 80,
        },
        summary: '由于API限制，返回默认分析结果。建议检查内容结构、增强引用价值、提高语义准确性。',
        suggestions: [
          {
            type: 'structure',
            originalText: '内容开头',
            suggestedText: '建议添加清晰的标题和概述',
            confidence: 0.8,
          },
          {
            type: 'citation',
            originalText: '关键信息',
            suggestedText: '建议将关键信息提炼为独立段落',
            confidence: 0.7,
          },
        ],
      };
    }
  }

  async getAnalysisHistory(contentId: string) {
    return this.analysisRepository.find({
      where: { contentId },
      order: { createdAt: 'DESC' },
      relations: ['suggestions'],
    });
  }
}