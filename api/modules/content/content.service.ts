import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Content } from '../../entities/content.entity';
import { CreateContentDto } from './dto/create-content.dto';
import { UpdateContentDto } from './dto/update-content.dto';
import { UserService } from '../user/user.service';
import * as cheerio from 'cheerio';

@Injectable()
export class ContentService {
  constructor(
    @InjectRepository(Content)
    private contentRepository: Repository<Content>,
    private userService: UserService,
  ) {}

  async create(userId: string, createContentDto: CreateContentDto): Promise<Content> {
    // 增加用户使用次数
    await this.userService.incrementUsageCount(userId);

    const content = this.contentRepository.create({
      ...createContentDto,
      userId,
      metadata: createContentDto.metadata ? JSON.stringify(createContentDto.metadata) : '{}',
    });

    return this.contentRepository.save(content);
  }

  async findAll(userId: string, page: number = 1, limit: number = 10) {
    const [contents, total] = await this.contentRepository.findAndCount({
      where: { userId: userId },
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
      relations: ['analyses'],
    });

    return {
      contents,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string, userId: string): Promise<Content> {
    const content = await this.contentRepository.findOne({
      where: { id },
      relations: ['analyses', 'analyses.suggestions'],
    });

    if (!content) {
      throw new NotFoundException('内容不存在');
    }

    if (content.userId !== userId) {
      throw new ForbiddenException('无权访问此内容');
    }

    return content;
  }

  async update(id: string, userId: string, updateContentDto: UpdateContentDto): Promise<Content> {
    const content = await this.findOne(id, userId);
    
    Object.assign(content, updateContentDto);
    return this.contentRepository.save(content);
  }

  async remove(id: string, userId: string): Promise<void> {
    const content = await this.findOne(id, userId);
    await this.contentRepository.remove(content);
  }

  async getContentStats(userId: string) {
    const totalContents = await this.contentRepository.count({ where: { userId } });
    const optimizedContents = await this.contentRepository.count({
      where: { userId, optimizedContent: 'NOT NULL' },
    });

    return {
      totalContents,
      optimizedContents,
      optimizationRate: totalContents > 0 ? (optimizedContents / totalContents) * 100 : 0,
    };
  }

  async fetchUrlContent(url: string) {
    console.log(`[URL抓取] 开始抓取URL: ${url}`);
    
    try {
      // 验证URL格式
      let validUrl: URL;
      try {
        validUrl = new URL(url);
        console.log(`[URL抓取] URL验证通过: ${validUrl.href}`);
      } catch (urlError) {
        console.error(`[URL抓取] URL格式无效: ${url}`, urlError);
        throw new Error(`URL格式无效: ${url}`);
      }

      // 检查协议
      if (!['http:', 'https:'].includes(validUrl.protocol)) {
        console.error(`[URL抓取] 不支持的协议: ${validUrl.protocol}`);
        throw new Error(`不支持的协议: ${validUrl.protocol}，仅支持 HTTP 和 HTTPS`);
      }

      console.log(`[URL抓取] 发送HTTP请求到: ${validUrl.href}`);
      const startTime = Date.now();
      
      // 创建带超时的fetch请求
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时
      
      const response = await fetch(validUrl.href, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
        },
        signal: controller.signal,
      });
      
      clearTimeout(timeoutId);

      const responseTime = Date.now() - startTime;
      console.log(`[URL抓取] HTTP响应: ${response.status} ${response.statusText} (${responseTime}ms)`);
      console.log(`[URL抓取] 响应头:`, Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorMsg = `HTTP ${response.status} ${response.statusText}`;
        console.error(`[URL抓取] HTTP错误: ${errorMsg}`);

        // 提供更友好的错误信息
        let userFriendlyMessage = '';
        switch (response.status) {
          case 404:
            userFriendlyMessage = '页面不存在 (404)，请检查URL是否正确';
            break;
          case 403:
            userFriendlyMessage = '访问被拒绝 (403)，该网站可能禁止爬虫访问';
            break;
          case 500:
            userFriendlyMessage = '服务器内部错误 (500)，请稍后重试';
            break;
          case 503:
            userFriendlyMessage = '服务不可用 (503)，请稍后重试';
            break;
          default:
            userFriendlyMessage = `HTTP错误: ${errorMsg}`;
        }

        throw new Error(userFriendlyMessage);
      }

      // 检查内容类型
      const contentType = response.headers.get('content-type') || '';
      console.log(`[URL抓取] 内容类型: ${contentType}`);
      
      if (!contentType.includes('text/html')) {
        console.warn(`[URL抓取] 警告: 内容类型不是HTML: ${contentType}`);
      }

      console.log(`[URL抓取] 开始解析HTML内容`);
      const html = await response.text();
      console.log(`[URL抓取] HTML内容长度: ${html.length} 字符`);
      
      if (html.length === 0) {
        throw new Error('响应内容为空');
      }

      const $ = cheerio.load(html);
      console.log(`[URL抓取] Cheerio解析完成`);

      // 提取标题
      let title = $('title').text().trim();
      console.log(`[URL抓取] 提取到title标签: "${title}"`);
      
      if (!title) {
        title = $('h1').first().text().trim();
        console.log(`[URL抓取] 从h1标签提取标题: "${title}"`);
      }
      if (!title) {
        title = '未找到标题';
        console.log(`[URL抓取] 未找到标题，使用默认值`);
      }

      // 提取主要内容
      let content = '';
      
      // 尝试从常见的内容容器中提取文本
      const contentSelectors = [
        'article',
        '[role="main"]',
        '.content',
        '.post-content',
        '.entry-content',
        '.article-content',
        'main',
        '.main-content',
      ];

      console.log(`[URL抓取] 尝试从内容选择器提取文本`);
      for (const selector of contentSelectors) {
        const element = $(selector);
        if (element.length > 0) {
          console.log(`[URL抓取] 找到选择器 "${selector}": ${element.length} 个元素`);
          // 移除脚本和样式标签
          element.find('script, style, nav, header, footer, aside').remove();
          content = element.text().trim();
          console.log(`[URL抓取] 从 "${selector}" 提取到 ${content.length} 字符`);
          if (content.length > 100) {
            console.log(`[URL抓取] 内容长度足够，使用此选择器的内容`);
            break;
          }
        }
      }

      // 如果没有找到合适的内容，尝试提取所有段落
      if (!content || content.length < 100) {
        console.log(`[URL抓取] 内容不足，尝试提取所有段落`);
        $('script, style, nav, header, footer, aside').remove();
        const paragraphs = $('p').map((i, el) => $(el).text().trim()).get();
        const validParagraphs = paragraphs.filter(p => p.length > 20);
        console.log(`[URL抓取] 找到 ${paragraphs.length} 个段落，其中 ${validParagraphs.length} 个有效段落`);
        content = validParagraphs.join('\n\n');
      }

      // 清理内容
      content = content
        .replace(/\s+/g, ' ')
        .replace(/\n\s*\n/g, '\n\n')
        .trim();

      console.log(`[URL抓取] 最终内容长度: ${content.length} 字符`);

      if (!content) {
        console.error(`[URL抓取] 无法提取页面内容`);
        throw new Error('无法提取页面内容');
      }

      const result = {
        title,
        content,
        url: validUrl.href,
      };
      
      console.log(`[URL抓取] 抓取成功完成: 标题="${title}", 内容长度=${content.length}`);
      return result;
      
    } catch (error) {
      console.error(`[URL抓取] 抓取失败 - URL: ${url}`);
      console.error(`[URL抓取] 错误类型: ${error.constructor.name}`);
      console.error(`[URL抓取] 错误消息: ${error.message}`);
      console.error(`[URL抓取] 错误堆栈:`, error.stack);
      
      // 根据错误类型提供更具体的错误信息
      let errorMessage = 'URL抓取失败';

      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        errorMessage = '网络连接失败，请检查URL是否可访问';
      } else if (error.message.includes('timeout') || error.name === 'AbortError') {
        errorMessage = '请求超时，请稍后重试';
      } else if (error.message.includes('页面不存在') || error.message.includes('访问被拒绝') || error.message.includes('服务器内部错误') || error.message.includes('服务不可用')) {
        // 这些是我们已经处理过的HTTP错误，直接传递
        errorMessage = error.message;
      } else if (error.message.includes('URL格式无效')) {
        errorMessage = error.message;
      } else if (error.message.includes('不支持的协议')) {
        errorMessage = error.message;
      } else if (error.message.includes('无法提取页面内容')) {
        errorMessage = '页面内容为空或无法解析，请检查URL是否指向有效的网页';
      } else {
        errorMessage = `URL抓取失败: ${error.message}`;
      }

      throw new Error(errorMessage);
    }
  }
}