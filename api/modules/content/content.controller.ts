import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { ContentService } from './content.service';
import { AnalysisService } from './analysis.service';
import { CreateContentDto } from './dto/create-content.dto';
import { UpdateContentDto } from './dto/update-content.dto';
import { AnalyzeContentDto } from './dto/analyze-content.dto';
import { FetchUrlDto } from './dto/fetch-url.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('content')
@UseGuards(JwtAuthGuard)
export class ContentController {
  constructor(
    private contentService: ContentService,
    private analysisService: AnalysisService,
  ) {}

  @Post()
  async create(@Request() req, @Body() createContentDto: CreateContentDto) {
    const content = await this.contentService.create(req.user.id, createContentDto);
    return {
      success: true,
      content,
    };
  }

  @Get()
  async findAll(
    @Request() req,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
  ) {
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 10;
    const result = await this.contentService.findAll(req.user.id, pageNum, limitNum);
    return {
      success: true,
      ...result,
    };
  }

  @Get('stats')
  async getStats(@Request() req) {
    const stats = await this.contentService.getContentStats(req.user.id);
    return {
      success: true,
      stats,
    };
  }

  @Get(':id')
  async findOne(@Request() req, @Param('id') id: string) {
    const content = await this.contentService.findOne(id, req.user.id);
    return {
      success: true,
      content,
    };
  }

  @Put(':id')
  async update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateContentDto: UpdateContentDto,
  ) {
    const content = await this.contentService.update(id, req.user.id, updateContentDto);
    return {
      success: true,
      content,
    };
  }

  @Delete(':id')
  async remove(@Request() req, @Param('id') id: string) {
    await this.contentService.remove(id, req.user.id);
    return {
      success: true,
      message: '内容删除成功',
    };
  }

  @Post(':id/analyze')
  async analyzeContent(
    @Request() req,
    @Param('id') id: string,
    @Body() analyzeDto: AnalyzeContentDto,
  ) {
    const result = await this.analysisService.analyzeContent(id, analyzeDto);
    return {
      success: true,
      ...result,
    };
  }

  @Get(':id/analysis-history')
  async getAnalysisHistory(@Request() req, @Param('id') id: string) {
    // 验证用户权限
    await this.contentService.findOne(id, req.user.id);
    
    const history = await this.analysisService.getAnalysisHistory(id);
    return {
      success: true,
      history,
    };
  }

  @Post('fetch-url')
  async fetchUrl(@Request() req, @Body() fetchUrlDto: FetchUrlDto) {
    try {
      const result = await this.contentService.fetchUrlContent(fetchUrlDto.url);
      return {
        success: true,
        ...result,
      };
    } catch (error) {
      console.error(`[API] URL抓取失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
        url: fetchUrlDto.url,
      };
    }
  }
}