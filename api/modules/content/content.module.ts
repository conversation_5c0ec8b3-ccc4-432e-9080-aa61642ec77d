import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ContentController } from './content.controller';
import { ContentService } from './content.service';
import { AnalysisService } from './analysis.service';
import { Content } from '../../entities/content.entity';
import { Analysis } from '../../entities/analysis.entity';
import { Suggestion } from '../../entities/suggestion.entity';
import { UserModule } from '../user/user.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Content, Analysis, Suggestion]),
    UserModule,
  ],
  controllers: [ContentController],
  providers: [ContentService, AnalysisService],
  exports: [ContentService, AnalysisService],
})
export class ContentModule {}