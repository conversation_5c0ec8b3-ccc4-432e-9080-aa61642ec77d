const axios = require('axios');

async function testUrlFetch() {
  const baseUrl = 'http://localhost:3002/api';
  
  // 测试用例
  const testCases = [
    {
      name: '有效URL测试',
      url: 'https://example.com',
      shouldSucceed: true
    },
    {
      name: '404错误测试',
      url: 'https://httpstat.us/404',
      shouldSucceed: false
    },
    {
      name: '无效URL测试',
      url: 'not-a-valid-url',
      shouldSucceed: false
    }
  ];

  console.log('开始测试URL抓取功能...\n');

  for (const testCase of testCases) {
    console.log(`🧪 ${testCase.name}`);
    console.log(`URL: ${testCase.url}`);
    
    try {
      const response = await axios.post(`${baseUrl}/content/fetch-url`, {
        url: testCase.url
      }, {
        headers: {
          'Content-Type': 'application/json',
          // 注意：实际使用时需要JWT token
          // 'Authorization': 'Bearer your-jwt-token'
        },
        timeout: 10000
      });

      console.log(`✅ 响应状态: ${response.status}`);
      console.log(`📄 响应数据:`, JSON.stringify(response.data, null, 2));
      
      if (testCase.shouldSucceed && response.data.success) {
        console.log(`✅ 测试通过: 成功抓取内容`);
      } else if (!testCase.shouldSucceed && !response.data.success) {
        console.log(`✅ 测试通过: 正确处理了错误`);
      } else {
        console.log(`❌ 测试失败: 结果与预期不符`);
      }
      
    } catch (error) {
      console.log(`❌ 请求失败:`, error.message);
      if (error.response) {
        console.log(`📄 错误响应:`, error.response.data);
      }
    }
    
    console.log('─'.repeat(50));
  }
}

// 运行测试
testUrlFetch().catch(console.error);
