import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from './modules/auth/auth.module';
import { ContentModule } from './modules/content/content.module';
import { SchemaModule } from './modules/schema/schema.module';
import { KeywordModule } from './modules/keyword/keyword.module';
import { RewriteModule } from './modules/rewrite/rewrite.module';
import { TrackingModule } from './modules/tracking/tracking.module';
import { UserModule } from './modules/user/user.module';
import { DatabaseConfig } from './config/database.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRootAsync({
      useClass: DatabaseConfig,
    }),
    AuthModule,
    UserModule,
    ContentModule,
    SchemaModule,
    KeywordModule,
    RewriteModule,
    TrackingModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}