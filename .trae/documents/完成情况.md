## 项目概览

GeoPilot是一个功能完整的内容GEO优化工具，帮助用户分析和优化内容的搜索引擎表现。

## 已完成功能

### 🔐 用户认证系统

* 用户注册和登录功能

* JWT令牌认证

* 安全的密码加密

* 用户状态管理

### 📊 内容GEO分析

* 智能内容分析和评分

* 结构化数据分析

* 语义清晰度检测

* AI驱动的优化建议

* 文件上传和URL内容抓取

### 🔍 关键词生成

* 基于内容的智能关键词提取

* 相关问题生成

* SEO优化建议

### ✍️ 内容重构引擎

* AI驱动的内容优化

* 可读性改进

* 结构化重写

### 🏗️ 结构化标注生成

* Schema.org标注生成

* 多种标注类型支持

* JSON-LD格式输出

### 📈 用户仪表板

* 分析历史记录

* 数据可视化图表

* 项目管理界面

## 技术架构

### 前端 (React + TypeScript)

* 框架 : React 18 + Vite

* 样式 : TailwindCSS + 响应式设计

* 状态管理 : Zustand

* 路由 : React Router

* 组件 : 完整的UI组件库

### 后端 (NestJS + TypeScript)

* 框架 : NestJS

* 数据库 : Supabase (PostgreSQL)

* ORM : TypeORM

* 认证 : JWT + bcrypt

* AI集成 : OpenAI API

## 服务器状态

* ✅ 前端服务器 : <http://localhost:5173> (正常运行)

* ✅ 后端API服务器 : <http://localhost:3002> (正常运行)

* ✅ 数据库 : Supabase连接正常

## 主要页面

1. <br />

   1. <br />

   首页 - 产品介绍和功能概览
2. <br />

   1. <br />

   登录/注册 - 用户认证
3. <br />

   1. <br />

   仪表板 - 用户主控制台
4. <br />

   1. <br />

   内容分析 - GEO分析工具
5. <br />

   1. <br />

   关键词生成 - SEO关键词工具
6. <br />

   1. <br />

   内容重构 - AI内容优化
7. <br />

   1. <br />

   结构化标注 - Schema生成器
   项目已完全可用，用户可以立即开始使用所有功能进行内容GEO优化！

<br />

前端启动

npm run client:dev

后端启动

npm run start:dev

PORT=3002 npm run start:dev

