# GeoPilot 技术架构文档

## 1. Architecture design

```mermaid
graph TD
    A[用户浏览器] --> B[React前端应用]
    B --> C[NestJS后端服务]
    C --> D[PostgreSQL数据库]
    C --> E[OpenAI GPT-4o API]
    C --> F[Google/Bing搜索API]
    C --> G[Stripe支付API]
    C --> H[Redis缓存]
    C --> I[向量数据库 pgvector]

    subgraph "前端层"
        B
    end

    subgraph "后端层"
        C
        H
    end

    subgraph "数据层"
        D
        I
    end

    subgraph "外部服务"
        E
        F
        G
    end
```

## 2. Technology Description

* Frontend: React\@18 + TypeScript + TailwindCSS\@3 + Vite + React Router

* Backend: NestJS\@10 + TypeScript + Express + LangChain

* Database: PostgreSQL\@15 + pgvector + Redis\@7

* AI Services: OpenAI GPT-4o + Embedding API

* External APIs: Google Search API + Bing Search API + Stripe API

* Deployment: Vercel (Frontend) + Railway (Backend) + Supabase (Database)

## 3. Route definitions

| Route      | Purpose      |
| ---------- | ------------ |
| /          | 首页，产品介绍和功能导航 |
| /login     | 用户登录页面       |
| /register  | 用户注册页面       |
| /dashboard | 用户仪表板，功能概览   |
| /analyze   | 内容GEO分析页面    |
| /schema    | 结构化标注生成器     |
| /extract   | 段落提取与重构页面    |
| /keywords  | 关键词和问题生成页面   |
| /rewrite   | AI内容重构引擎     |
| /manage    | 内容管理中心       |
| /tracking  | GEO曝光追踪系统    |
| /settings  | 用户设置和订阅管理    |
| /admin     | 管理员后台        |

## 4. API definitions

### 4.1 Core API

**用户认证相关**

```
POST /api/auth/login
```

Request:

| Param Name | Param Type | isRequired | Description |
| ---------- | ---------- | ---------- | ----------- |
| email      | string     | true       | 用户邮箱        |
| password   | string     | true       | 用户密码        |

Response:

| Param Name | Param Type | Description |
| ---------- | ---------- | ----------- |
| success    | boolean    | 登录是否成功      |
| token      | string     | JWT访问令牌     |
| user       | object     | 用户信息        |

**内容分析相关**

```
POST /api/content/analyze
```

Request:

| Param Name | Param Type | isRequired | Description                |
| ---------- | ---------- | ---------- | -------------------------- |
| content    | string     | true       | 待分析的文本内容                   |
| url        | string     | false      | 内容来源URL                    |
| type       | string     | false      | 内容类型(article/blog/product) |

Response:

| Param Name  | Param Type | Description |
| ----------- | ---------- | ----------- |
| score       | object     | GEO评分结果     |
| suggestions | array      | 优化建议列表      |
| segments    | array      | 内容分段结果      |

**结构化标注相关**

```
POST /api/schema/generate
```

Request:

| Param Name | Param Type | isRequired | Description                     |
| ---------- | ---------- | ---------- | ------------------------------- |
| type       | string     | true       | 标注类型(FAQ/HowTo/Product/Article) |
| data       | object     | true       | 结构化数据                           |

Response:

| Param Name | Param Type | Description  |
| ---------- | ---------- | ------------ |
| jsonld     | string     | 生成的JSON-LD代码 |
| preview    | string     | 预览HTML       |

**关键词生成相关**

```
POST /api/keywords/generate
```

Request:

| Param Name | Param Type | isRequired | Description |
| ---------- | ---------- | ---------- | ----------- |
| content    | string     | true       | 内容文本        |
| count      | number     | false      | 生成关键词数量     |

Response:

| Param Name | Param Type | Description |
| ---------- | ---------- | ----------- |
| keywords   | array      | 关键词列表       |
| questions  | array      | 相关问题列表      |

**引用追踪相关**

```
POST /api/tracking/monitor
```

Request:

| Param Name | Param Type | isRequired | Description |
| ---------- | ---------- | ---------- | ----------- |
| contentId  | string     | true       | 内容ID        |
| platforms  | array      | false      | 监测平台列表      |

Response:

| Param Name | Param Type | Description |
| ---------- | ---------- | ----------- |
| taskId     | string     | 监测任务ID      |
| status     | string     | 任务状态        |

Example Request:

```json
{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

Example Response:

```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user123",
    "email": "<EMAIL>",
    "plan": "premium"
  }
}
```

## 5. Server architecture diagram

```mermaid
graph TD
    A[客户端请求] --> B[Controller层]
    B --> C[Service层]
    C --> D[Repository层]
    D --> E[(PostgreSQL数据库)]
    
    C --> F[AI Service]
    F --> G[OpenAI API]
    
    C --> H[Search Service]
    H --> I[Google/Bing API]
    
    C --> J[Cache Service]
    J --> K[(Redis缓存)]
    
    C --> L[Payment Service]
    L --> M[Stripe API]

    subgraph "NestJS后端服务"
        B
        C
        D
        F
        H
        J
        L
    end
```

## 6. Data model

### 6.1 Data model definition

```mermaid
erDiagram
    USERS ||--o{ CONTENTS : creates
    USERS ||--o{ SUBSCRIPTIONS : has
    CONTENTS ||--o{ ANALYSES : analyzed_by
    CONTENTS ||--o{ TRACKING_TASKS : tracked_by
    ANALYSES ||--o{ SUGGESTIONS : contains
    TRACKING_TASKS ||--o{ TRACKING_RESULTS : produces
    
    USERS {
        uuid id PK
        string email UK
        string password_hash
        string name
        string plan
        integer usage_count
        timestamp created_at
        timestamp updated_at
    }
    
    CONTENTS {
        uuid id PK
        uuid user_id FK
        string title
        text original_content
        text optimized_content
        string content_type
        string source_url
        jsonb metadata
        timestamp created_at
        timestamp updated_at
    }
    
    ANALYSES {
        uuid id PK
        uuid content_id FK
        jsonb scores
        text analysis_result
        string model_version
        timestamp created_at
    }
    
    SUGGESTIONS {
        uuid id PK
        uuid analysis_id FK
        string suggestion_type
        text original_text
        text suggested_text
        float confidence_score
        timestamp created_at
    }
    
    TRACKING_TASKS {
        uuid id PK
        uuid content_id FK
        string status
        jsonb platforms
        jsonb queries
        timestamp created_at
        timestamp updated_at
    }
    
    TRACKING_RESULTS {
        uuid id PK
        uuid task_id FK
        string platform
        string query
        boolean found_citation
        text ai_response
        float similarity_score
        timestamp checked_at
    }
    
    SUBSCRIPTIONS {
        uuid id PK
        uuid user_id FK
        string plan_type
        string status
        timestamp start_date
        timestamp end_date
        string stripe_subscription_id
    }
```

### 6.2 Data Definition Language

**用户表 (users)**

```sql
-- 创建用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    plan VARCHAR(20) DEFAULT 'free' CHECK (plan IN ('free', 'basic', 'premium', 'enterprise')),
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_plan ON users(plan);
```

**内容表 (contents)**

```sql
-- 创建内容表
CREATE TABLE contents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    original_content TEXT NOT NULL,
    optimized_content TEXT,
    content_type VARCHAR(50) DEFAULT 'article',
    source_url VARCHAR(1000),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_contents_user_id ON contents(user_id);
CREATE INDEX idx_contents_created_at ON contents(created_at DESC);
CREATE INDEX idx_contents_content_type ON contents(content_type);
```

**分析结果表 (analyses)**

```sql
-- 创建分析表
CREATE TABLE analyses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID NOT NULL REFERENCES contents(id) ON DELETE CASCADE,
    scores JSONB NOT NULL DEFAULT '{}',
    analysis_result TEXT,
    model_version VARCHAR(50) DEFAULT 'gpt-4o',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_analyses_content_id ON analyses(content_id);
CREATE INDEX idx_analyses_created_at ON analyses(created_at DESC);
```

**优化建议表 (suggestions)**

```sql
-- 创建建议表
CREATE TABLE suggestions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    analysis_id UUID NOT NULL REFERENCES analyses(id) ON DELETE CASCADE,
    suggestion_type VARCHAR(50) NOT NULL,
    original_text TEXT NOT NULL,
    suggested_text TEXT NOT NULL,
    confidence_score FLOAT DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_suggestions_analysis_id ON suggestions(analysis_id);
CREATE INDEX idx_suggestions_type ON suggestions(suggestion_type);
```

**追踪任务表 (tracking\_tasks)**

```sql
-- 创建追踪任务表
CREATE TABLE tracking_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID NOT NULL REFERENCES contents(id) ON DELETE CASCADE,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    platforms JSONB DEFAULT '[]',
    queries JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_tracking_tasks_content_id ON tracking_tasks(content_id);
CREATE INDEX idx_tracking_tasks_status ON tracking_tasks(status);
```

**追踪结果表 (tracking\_results)**

```sql
-- 创建追踪结果表
CREATE TABLE tracking_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES tracking_tasks(id) ON DELETE CASCADE,
    platform VARCHAR(50) NOT NULL,
    query TEXT NOT NULL,
    found_citation BOOLEAN DEFAULT FALSE,
    ai_response TEXT,
    similarity_score FLOAT DEFAULT 0.0,
    checked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_tracking_results_task_id ON tracking_results(task_id);
CREATE INDEX idx_tracking_results_platform ON tracking_results(platform);
CREATE INDEX idx_tracking_results_found_citation ON tracking_results(found_citation);
```

**订阅表 (subscriptions)**

```sql
-- 创建订阅表
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    plan_type VARCHAR(20) NOT NULL CHECK (plan_type IN ('basic', 'premium', 'enterprise')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'canceled', 'expired')),
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    stripe_subscription_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);
CREATE INDEX idx_subscriptions_stripe_id ON subscriptions(stripe_subscription_id);
```

**向量扩展和嵌入表**

```sql
-- 启用向量扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- 创建内容嵌入表
CREATE TABLE content_embeddings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID NOT NULL REFERENCES contents(id) ON DELETE CASCADE,
    embedding vector(1536),
    model_name VARCHAR(50) DEFAULT 'text-embedding-ada-002',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建向量索引
CREATE INDEX idx_content_embeddings_vector ON content_embeddings USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX idx_content_embeddings_content_id ON content_embeddings(content_id);
```

**初始化数据**

```sql
-- 插入管理员用户
INSERT INTO users (email, password_hash, name, plan) VALUES 
('<EMAIL>', '$2b$10$example_hash', 'System Admin', 'enterprise');

-- 插入示例内容类型配置
INSERT INTO contents (user_id, title, original_content, content_type) VALUES 
((SELECT id FROM users WHERE email = '<EMAIL>'), 
 '示例文章', 
 '这是一篇关于GEO优化的示例文章...', 
 'article');
```

