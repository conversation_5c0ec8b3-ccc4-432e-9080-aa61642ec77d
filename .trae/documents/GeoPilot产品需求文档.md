# GeoPilot 技术架构文档

## 1. Architecture design

```mermaid
graph TD
    A[用户浏览器] --> B[React前端应用]
    B --> C[NestJS后端服务]
    C --> D[PostgreSQL数据库]
    C --> E[OpenAI GPT-4o API]
    C --> F[Google/Bing搜索API]
    C --> G[Stripe支付API]
    C --> H[Redis缓存]
    C --> I[向量数据库 pgvector]

    subgraph "前端层"
        B
    end

    subgraph "后端层"
        C
        H
    end

    subgraph "数据层"
        D
        I
    end

    subgraph "外部服务"
        E
        F
        G
    end
```

## 2. Technology Description

* Frontend: React\@18 + TypeScript + TailwindCSS\@3 + Vite + React Router

* Backend: NestJS\@10 + TypeScript + Express + LangChain

* Database: PostgreSQL\@15 + pgvector + Redis\@7

* AI Services: OpenAI GPT-4o + Embedding API

* External APIs: Google Search API + Bing Search API + Stripe API

* Deployment: Vercel (Frontend) + Railway (Backend) + Su

