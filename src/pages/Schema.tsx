import React, { useState } from 'react';
import { Code, Copy, Download, CheckCircle, AlertCircle, Lightbulb } from 'lucide-react';
import { toast } from 'sonner';
import { useAuthStore } from '../stores/authStore';

interface SchemaResult {
  schema: any;
  schemaType: string;
  generatedAt: string;
  recommendations: string[];
  error?: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

const Schema: React.FC = () => {
  const { token } = useAuthStore();
  const [content, setContent] = useState('');
  const [schemaType, setSchemaType] = useState('article');
  const [targetAudience, setTargetAudience] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [schemaResult, setSchemaResult] = useState<SchemaResult | null>(null);
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);

  const schemaTypes = [
    { value: 'article', label: '文章 (Article)' },
    { value: 'faq', label: '常见问题 (FAQ)' },
    { value: 'howto', label: '操作指南 (HowTo)' },
    { value: 'product', label: '产品 (Product)' },
    { value: 'organization', label: '组织 (Organization)' }
  ];

  const handleGenerateSchema = async () => {
    if (!content.trim()) {
      toast.error('请输入内容');
      return;
    }

    setIsGenerating(true);
    setValidationResult(null);

    try {
      const response = await fetch('/api/schema/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          content,
          schemaType,
          targetAudience
        })
      });

      if (!response.ok) {
        throw new Error('生成失败');
      }

      const data = await response.json();
      setSchemaResult(data);
      toast.success('结构化标注生成成功！');
    } catch (error) {
      console.error('生成失败:', error);
      toast.error('生成失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleValidateSchema = async () => {
    if (!schemaResult?.schema) {
      toast.error('请先生成结构化标注');
      return;
    }

    setIsValidating(true);

    try {
      const response = await fetch('/api/schema/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          schema: schemaResult.schema
        })
      });

      if (!response.ok) {
        throw new Error('验证失败');
      }

      const data = await response.json();
      setValidationResult(data.validation);
      
      if (data.validation.isValid) {
        toast.success('结构化标注验证通过！');
      } else {
        toast.warning('结构化标注存在问题，请查看详情');
      }
    } catch (error) {
      console.error('验证失败:', error);
      toast.error('验证失败，请重试');
    } finally {
      setIsValidating(false);
    }
  };

  const handleCopySchema = () => {
    if (schemaResult?.schema) {
      navigator.clipboard.writeText(JSON.stringify(schemaResult.schema, null, 2));
      toast.success('已复制到剪贴板');
    }
  };

  const handleDownloadSchema = () => {
    if (schemaResult?.schema) {
      const blob = new Blob([JSON.stringify(schemaResult.schema, null, 2)], {
        type: 'application/json'
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `schema-${schemaResult.schemaType}-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success('文件下载成功');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">结构化标注生成器</h1>
          <p className="text-gray-600">生成符合Schema.org标准的JSON-LD结构化数据，提升搜索引擎优化效果</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 输入区域 */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">内容输入</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    内容文本
                  </label>
                  <textarea
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    placeholder="请输入需要生成结构化标注的内容..."
                    className="w-full h-40 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Schema类型
                  </label>
                  <select
                    value={schemaType}
                    onChange={(e) => setSchemaType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {schemaTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    目标受众 (可选)
                  </label>
                  <input
                    type="text"
                    value={targetAudience}
                    onChange={(e) => setTargetAudience(e.target.value)}
                    placeholder="例如：技术开发者、普通用户等"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <button
                  onClick={handleGenerateSchema}
                  disabled={isGenerating || !content.trim()}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                >
                  {isGenerating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      生成中...
                    </>
                  ) : (
                    <>
                      <Code className="h-4 w-4 mr-2" />
                      生成结构化标注
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* 优化建议 */}
            {schemaResult?.recommendations && (
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Lightbulb className="h-5 w-5 text-yellow-500 mr-2" />
                  优化建议
                </h3>
                <ul className="space-y-2">
                  {schemaResult.recommendations.map((recommendation, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-600">{recommendation}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* 结果区域 */}
          <div className="space-y-6">
            {schemaResult && (
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">生成结果</h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={handleValidateSchema}
                      disabled={isValidating}
                      className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-400 transition-colors flex items-center"
                    >
                      {isValidating ? (
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
                      ) : (
                        <CheckCircle className="h-3 w-3 mr-1" />
                      )}
                      验证
                    </button>
                    <button
                      onClick={handleCopySchema}
                      className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors flex items-center"
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      复制
                    </button>
                    <button
                      onClick={handleDownloadSchema}
                      className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors flex items-center"
                    >
                      <Download className="h-3 w-3 mr-1" />
                      下载
                    </button>
                  </div>
                </div>

                {schemaResult.error && (
                  <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <div className="flex items-center">
                      <AlertCircle className="h-4 w-4 text-yellow-600 mr-2" />
                      <span className="text-sm text-yellow-800">{schemaResult.error}</span>
                    </div>
                  </div>
                )}

                <div className="bg-gray-50 rounded-md p-4">
                  <pre className="text-sm text-gray-800 overflow-x-auto whitespace-pre-wrap">
                    {JSON.stringify(schemaResult.schema, null, 2)}
                  </pre>
                </div>

                <div className="mt-4 text-xs text-gray-500">
                  类型: {schemaResult.schemaType} | 生成时间: {new Date(schemaResult.generatedAt).toLocaleString()}
                </div>
              </div>
            )}

            {/* 验证结果 */}
            {validationResult && (
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">验证结果</h3>
                
                <div className={`p-4 rounded-md ${
                  validationResult.isValid 
                    ? 'bg-green-50 border border-green-200' 
                    : 'bg-red-50 border border-red-200'
                }`}>
                  <div className="flex items-center mb-2">
                    {validationResult.isValid ? (
                      <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                    )}
                    <span className={`font-medium ${
                      validationResult.isValid ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {validationResult.isValid ? '验证通过' : '验证失败'}
                    </span>
                  </div>
                  
                  {!validationResult.isValid && validationResult.errors.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-red-700 mb-2">发现以下问题：</p>
                      <ul className="list-disc list-inside space-y-1">
                        {validationResult.errors.map((error, index) => (
                          <li key={index} className="text-sm text-red-600">{error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 使用说明 */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">使用说明</h3>
              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-start">
                  <span className="font-medium text-gray-900 mr-2">1.</span>
                  <span>输入需要生成结构化标注的内容文本</span>
                </div>
                <div className="flex items-start">
                  <span className="font-medium text-gray-900 mr-2">2.</span>
                  <span>选择合适的Schema类型（文章、FAQ、操作指南等）</span>
                </div>
                <div className="flex items-start">
                  <span className="font-medium text-gray-900 mr-2">3.</span>
                  <span>可选择性填写目标受众以获得更精准的结果</span>
                </div>
                <div className="flex items-start">
                  <span className="font-medium text-gray-900 mr-2">4.</span>
                  <span>生成后可验证、复制或下载JSON-LD代码</span>
                </div>
                <div className="flex items-start">
                  <span className="font-medium text-gray-900 mr-2">5.</span>
                  <span>将生成的代码添加到网页的 &lt;head&gt; 标签中</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Schema;