import React, { useState } from 'react';
import { Refresh<PERSON><PERSON>, Zap, BookOpen, Copy, Download, FileText, Target, BarChart3 } from 'lucide-react';
import { toast } from 'sonner';
import { useAuthStore } from '../stores/authStore';

interface RewriteResult {
  originalContent: string;
  rewrittenContent: string;
  improvements: {
    readabilityScore: number;
    geoScore: number;
    structureScore: number;
    engagementScore: number;
  };
  changes: {
    type: 'structure' | 'clarity' | 'engagement' | 'geo' | 'readability';
    description: string;
    impact: 'high' | 'medium' | 'low';
  }[];
  suggestions: string[];
  wordCount: {
    original: number;
    rewritten: number;
    change: number;
  };
  generatedAt: string;
  error?: string;
}

interface OptimizeResult {
  originalContent: string;
  optimizedContent: string;
  optimizations: {
    seoScore: number;
    keywordDensity: number;
    readabilityScore: number;
    structureScore: number;
  };
  appliedOptimizations: {
    type: 'keyword' | 'structure' | 'meta' | 'readability';
    description: string;
    value: string;
  }[];
  recommendations: string[];
  generatedAt: string;
  error?: string;
}

interface ReadabilityResult {
  originalContent: string;
  improvedContent: string;
  readabilityMetrics: {
    fleschScore: number;
    averageSentenceLength: number;
    averageWordsPerSentence: number;
    complexWordsPercentage: number;
  };
  improvements: {
    type: 'sentence' | 'vocabulary' | 'structure' | 'flow';
    description: string;
    before: string;
    after: string;
  }[];
  suggestions: string[];
  generatedAt: string;
  error?: string;
}

const Rewrite: React.FC = () => {
  const { token } = useAuthStore();
  const [activeTab, setActiveTab] = useState<'rewrite' | 'optimize' | 'readability'>('rewrite');
  
  // Rewrite tab state
  const [content, setContent] = useState('');
  const [style, setStyle] = useState('professional');
  const [tone, setTone] = useState('neutral');
  const [targetAudience, setTargetAudience] = useState('');
  const [preserveLength, setPreserveLength] = useState(false);
  const [isRewriting, setIsRewriting] = useState(false);
  const [rewriteResult, setRewriteResult] = useState<RewriteResult | null>(null);
  
  // Optimize tab state
  const [optimizeContent, setOptimizeContent] = useState('');
  const [keywords, setKeywords] = useState('');
  const [targetSEO, setTargetSEO] = useState(true);
  const [focusArea, setFocusArea] = useState('overall');
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [optimizeResult, setOptimizeResult] = useState<OptimizeResult | null>(null);
  
  // Readability tab state
  const [readabilityContent, setReadabilityContent] = useState('');
  const [targetLevel, setTargetLevel] = useState('intermediate');
  const [simplifyLanguage, setSimplifyLanguage] = useState(true);
  const [improveSentenceStructure, setImproveSentenceStructure] = useState(true);
  const [isImprovingReadability, setIsImprovingReadability] = useState(false);
  const [readabilityResult, setReadabilityResult] = useState<ReadabilityResult | null>(null);

  const styleOptions = [
    { value: 'professional', label: '专业' },
    { value: 'casual', label: '随意' },
    { value: 'academic', label: '学术' },
    { value: 'creative', label: '创意' },
    { value: 'technical', label: '技术' }
  ];

  const toneOptions = [
    { value: 'neutral', label: '中性' },
    { value: 'friendly', label: '友好' },
    { value: 'formal', label: '正式' },
    { value: 'persuasive', label: '说服性' },
    { value: 'informative', label: '信息性' }
  ];

  const focusAreaOptions = [
    { value: 'overall', label: '整体优化' },
    { value: 'keywords', label: '关键词' },
    { value: 'structure', label: '结构' },
    { value: 'readability', label: '可读性' },
    { value: 'engagement', label: '参与度' }
  ];

  const targetLevelOptions = [
    { value: 'beginner', label: '初级' },
    { value: 'intermediate', label: '中级' },
    { value: 'advanced', label: '高级' },
    { value: 'expert', label: '专家' }
  ];

  const handleRewrite = async () => {
    if (!content.trim()) {
      toast.error('请输入内容');
      return;
    }

    setIsRewriting(true);

    try {
      const response = await fetch('/api/rewrite/content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          content,
          style,
          tone,
          targetAudience: targetAudience || undefined,
          preserveLength
        })
      });

      if (!response.ok) {
        throw new Error('重构失败');
      }

      const data = await response.json();
      setRewriteResult(data.data);
      toast.success('内容重构成功！');
    } catch (error) {
      console.error('重构失败:', error);
      toast.error('重构失败，请重试');
    } finally {
      setIsRewriting(false);
    }
  };

  const handleOptimize = async () => {
    if (!optimizeContent.trim()) {
      toast.error('请输入内容');
      return;
    }

    setIsOptimizing(true);

    try {
      const keywordList = keywords.split(',').map(k => k.trim()).filter(k => k);
      
      const response = await fetch('/api/rewrite/optimize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          content: optimizeContent,
          keywords: keywordList,
          targetSEO,
          focusArea
        })
      });

      if (!response.ok) {
        throw new Error('优化失败');
      }

      const data = await response.json();
      setOptimizeResult(data.data);
      toast.success('内容优化成功！');
    } catch (error) {
      console.error('优化失败:', error);
      toast.error('优化失败，请重试');
    } finally {
      setIsOptimizing(false);
    }
  };

  const handleImproveReadability = async () => {
    if (!readabilityContent.trim()) {
      toast.error('请输入内容');
      return;
    }

    setIsImprovingReadability(true);

    try {
      const response = await fetch('/api/rewrite/readability', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          content: readabilityContent,
          targetLevel,
          simplifyLanguage,
          improveSentenceStructure
        })
      });

      if (!response.ok) {
        throw new Error('可读性优化失败');
      }

      const data = await response.json();
      setReadabilityResult(data.data);
      toast.success('可读性优化成功！');
    } catch (error) {
      console.error('可读性优化失败:', error);
      toast.error('可读性优化失败，请重试');
    } finally {
      setIsImprovingReadability(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('已复制到剪贴板');
  };

  const exportData = (data: any, filename: string) => {
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${filename}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('文件导出成功');
  };

  const getImpactColor = (impact: string) => {
    const colors = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800'
    };
    return colors[impact] || 'bg-gray-100 text-gray-800';
  };

  const getChangeTypeColor = (type: string) => {
    const colors = {
      structure: 'bg-blue-100 text-blue-800',
      clarity: 'bg-green-100 text-green-800',
      engagement: 'bg-purple-100 text-purple-800',
      geo: 'bg-orange-100 text-orange-800',
      readability: 'bg-pink-100 text-pink-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getOptimizationTypeColor = (type: string) => {
    const colors = {
      keyword: 'bg-blue-100 text-blue-800',
      structure: 'bg-green-100 text-green-800',
      meta: 'bg-purple-100 text-purple-800',
      readability: 'bg-orange-100 text-orange-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getImprovementTypeColor = (type: string) => {
    const colors = {
      sentence: 'bg-blue-100 text-blue-800',
      vocabulary: 'bg-green-100 text-green-800',
      structure: 'bg-purple-100 text-purple-800',
      flow: 'bg-orange-100 text-orange-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">AI 内容重构引擎</h1>
          <p className="text-gray-600">使用AI技术重构、优化和改进您的内容</p>
        </div>

        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('rewrite')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'rewrite'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <RefreshCw className="h-4 w-4 inline mr-2" />
                内容重构
              </button>
              <button
                onClick={() => setActiveTab('optimize')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'optimize'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Zap className="h-4 w-4 inline mr-2" />
                SEO优化
              </button>
              <button
                onClick={() => setActiveTab('readability')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'readability'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <BookOpen className="h-4 w-4 inline mr-2" />
                可读性优化
              </button>
            </nav>
          </div>
        </div>

        {/* Rewrite Tab */}
        {activeTab === 'rewrite' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Input Section */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow p-6 sticky top-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">内容重构</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      原始内容
                    </label>
                    <textarea
                      value={content}
                      onChange={(e) => setContent(e.target.value)}
                      placeholder="请输入需要重构的内容..."
                      className="w-full h-40 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      写作风格
                    </label>
                    <select
                      value={style}
                      onChange={(e) => setStyle(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {styleOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      语调
                    </label>
                    <select
                      value={tone}
                      onChange={(e) => setTone(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {toneOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      目标受众 (可选)
                    </label>
                    <input
                      type="text"
                      value={targetAudience}
                      onChange={(e) => setTargetAudience(e.target.value)}
                      placeholder="例如：技术开发者、企业主等"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="preserveLength"
                      checked={preserveLength}
                      onChange={(e) => setPreserveLength(e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="preserveLength" className="ml-2 block text-sm text-gray-700">
                      保持原文长度
                    </label>
                  </div>

                  <button
                    onClick={handleRewrite}
                    disabled={isRewriting || !content.trim()}
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                  >
                    {isRewriting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        重构中...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        开始重构
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Results Section */}
            <div className="lg:col-span-2">
              {rewriteResult && (
                <div className="space-y-6">
                  {/* Header */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">重构结果</h3>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => copyToClipboard(rewriteResult.rewrittenContent)}
                          className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors flex items-center"
                        >
                          <Copy className="h-3 w-3 mr-1" />
                          复制
                        </button>
                        <button
                          onClick={() => exportData(rewriteResult, 'rewrite-result')}
                          className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors flex items-center"
                        >
                          <Download className="h-3 w-3 mr-1" />
                          导出
                        </button>
                      </div>
                    </div>

                    {rewriteResult.error && (
                      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                        <span className="text-sm text-yellow-800">{rewriteResult.error}</span>
                      </div>
                    )}

                    {/* Content Comparison */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">原始内容</h4>
                        <div className="bg-gray-50 p-4 rounded-lg h-40 overflow-y-auto">
                          <p className="text-sm text-gray-700 whitespace-pre-wrap">{rewriteResult.originalContent}</p>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">重构后内容</h4>
                        <div className="bg-blue-50 p-4 rounded-lg h-40 overflow-y-auto">
                          <p className="text-sm text-gray-700 whitespace-pre-wrap">{rewriteResult.rewrittenContent}</p>
                        </div>
                      </div>
                    </div>

                    {/* Improvements Scores */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{rewriteResult.improvements.readabilityScore}</div>
                        <div className="text-sm text-gray-600">可读性</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{rewriteResult.improvements.geoScore}</div>
                        <div className="text-sm text-gray-600">GEO评分</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">{rewriteResult.improvements.structureScore}</div>
                        <div className="text-sm text-gray-600">结构</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">{rewriteResult.improvements.engagementScore}</div>
                        <div className="text-sm text-gray-600">参与度</div>
                      </div>
                    </div>

                    {/* Word Count */}
                    <div className="bg-gray-50 p-4 rounded-lg mb-6">
                      <h4 className="font-medium text-gray-900 mb-2">字数统计</h4>
                      <div className="flex items-center space-x-6 text-sm text-gray-600">
                        <span>原始: {rewriteResult.wordCount.original} 词</span>
                        <span>重构后: {rewriteResult.wordCount.rewritten} 词</span>
                        <span className={`font-medium ${
                          rewriteResult.wordCount.change > 0 ? 'text-green-600' : 
                          rewriteResult.wordCount.change < 0 ? 'text-red-600' : 'text-gray-600'
                        }`}>
                          变化: {rewriteResult.wordCount.change > 0 ? '+' : ''}{rewriteResult.wordCount.change} 词
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Changes */}
                  {rewriteResult.changes && rewriteResult.changes.length > 0 && (
                    <div className="bg-white rounded-lg shadow p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">主要改进</h3>
                      <div className="space-y-3">
                        {rewriteResult.changes.map((change, index) => (
                          <div key={index} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className={`px-2 py-1 text-xs rounded-full ${getChangeTypeColor(change.type)}`}>
                                {change.type === 'structure' ? '结构' :
                                 change.type === 'clarity' ? '清晰度' :
                                 change.type === 'engagement' ? '参与度' :
                                 change.type === 'geo' ? 'GEO' : '可读性'}
                              </span>
                              <span className={`px-2 py-1 text-xs rounded-full ${getImpactColor(change.impact)}`}>
                                {change.impact === 'high' ? '高影响' :
                                 change.impact === 'medium' ? '中影响' : '低影响'}
                              </span>
                            </div>
                            <p className="text-sm text-gray-700">{change.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Suggestions */}
                  {rewriteResult.suggestions && rewriteResult.suggestions.length > 0 && (
                    <div className="bg-white rounded-lg shadow p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">优化建议</h3>
                      <ul className="space-y-2">
                        {rewriteResult.suggestions.map((suggestion, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-blue-600 mr-2">•</span>
                            <span className="text-sm text-gray-600">{suggestion}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Optimize Tab */}
        {activeTab === 'optimize' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Input Section */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow p-6 sticky top-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">SEO优化</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      内容文本
                    </label>
                    <textarea
                      value={optimizeContent}
                      onChange={(e) => setOptimizeContent(e.target.value)}
                      placeholder="请输入需要优化的内容..."
                      className="w-full h-40 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      目标关键词 (用逗号分隔)
                    </label>
                    <input
                      type="text"
                      value={keywords}
                      onChange={(e) => setKeywords(e.target.value)}
                      placeholder="例如：SEO优化, 内容营销, 搜索引擎"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      优化重点
                    </label>
                    <select
                      value={focusArea}
                      onChange={(e) => setFocusArea(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {focusAreaOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="targetSEO"
                      checked={targetSEO}
                      onChange={(e) => setTargetSEO(e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="targetSEO" className="ml-2 block text-sm text-gray-700">
                      启用SEO优化
                    </label>
                  </div>

                  <button
                    onClick={handleOptimize}
                    disabled={isOptimizing || !optimizeContent.trim()}
                    className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                  >
                    {isOptimizing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        优化中...
                      </>
                    ) : (
                      <>
                        <Zap className="h-4 w-4 mr-2" />
                        开始优化
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Results Section */}
            <div className="lg:col-span-2">
              {optimizeResult && (
                <div className="space-y-6">
                  {/* Header */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">优化结果</h3>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => copyToClipboard(optimizeResult.optimizedContent)}
                          className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors flex items-center"
                        >
                          <Copy className="h-3 w-3 mr-1" />
                          复制
                        </button>
                        <button
                          onClick={() => exportData(optimizeResult, 'optimize-result')}
                          className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors flex items-center"
                        >
                          <Download className="h-3 w-3 mr-1" />
                          导出
                        </button>
                      </div>
                    </div>

                    {optimizeResult.error && (
                      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                        <span className="text-sm text-yellow-800">{optimizeResult.error}</span>
                      </div>
                    )}

                    {/* Content Comparison */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">原始内容</h4>
                        <div className="bg-gray-50 p-4 rounded-lg h-40 overflow-y-auto">
                          <p className="text-sm text-gray-700 whitespace-pre-wrap">{optimizeResult.originalContent}</p>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">优化后内容</h4>
                        <div className="bg-green-50 p-4 rounded-lg h-40 overflow-y-auto">
                          <p className="text-sm text-gray-700 whitespace-pre-wrap">{optimizeResult.optimizedContent}</p>
                        </div>
                      </div>
                    </div>

                    {/* Optimization Scores */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{optimizeResult.optimizations.seoScore}</div>
                        <div className="text-sm text-gray-600">SEO评分</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{optimizeResult.optimizations.keywordDensity}%</div>
                        <div className="text-sm text-gray-600">关键词密度</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">{optimizeResult.optimizations.readabilityScore}</div>
                        <div className="text-sm text-gray-600">可读性</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">{optimizeResult.optimizations.structureScore}</div>
                        <div className="text-sm text-gray-600">结构</div>
                      </div>
                    </div>
                  </div>

                  {/* Applied Optimizations */}
                  {optimizeResult.appliedOptimizations && optimizeResult.appliedOptimizations.length > 0 && (
                    <div className="bg-white rounded-lg shadow p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">应用的优化</h3>
                      <div className="space-y-3">
                        {optimizeResult.appliedOptimizations.map((optimization, index) => (
                          <div key={index} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className={`px-2 py-1 text-xs rounded-full ${getOptimizationTypeColor(optimization.type)}`}>
                                {optimization.type === 'keyword' ? '关键词' :
                                 optimization.type === 'structure' ? '结构' :
                                 optimization.type === 'meta' ? '元数据' : '可读性'}
                              </span>
                              <span className="text-xs text-gray-500">{optimization.value}</span>
                            </div>
                            <p className="text-sm text-gray-700">{optimization.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Recommendations */}
                  {optimizeResult.recommendations && optimizeResult.recommendations.length > 0 && (
                    <div className="bg-white rounded-lg shadow p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">优化建议</h3>
                      <ul className="space-y-2">
                        {optimizeResult.recommendations.map((recommendation, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-green-600 mr-2">•</span>
                            <span className="text-sm text-gray-600">{recommendation}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Readability Tab */}
        {activeTab === 'readability' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Input Section */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow p-6 sticky top-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">可读性优化</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      内容文本
                    </label>
                    <textarea
                      value={readabilityContent}
                      onChange={(e) => setReadabilityContent(e.target.value)}
                      placeholder="请输入需要优化可读性的内容..."
                      className="w-full h-40 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      目标阅读水平
                    </label>
                    <select
                      value={targetLevel}
                      onChange={(e) => setTargetLevel(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {targetLevelOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="simplifyLanguage"
                      checked={simplifyLanguage}
                      onChange={(e) => setSimplifyLanguage(e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="simplifyLanguage" className="ml-2 block text-sm text-gray-700">
                      简化语言
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="improveSentenceStructure"
                      checked={improveSentenceStructure}
                      onChange={(e) => setImproveSentenceStructure(e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="improveSentenceStructure" className="ml-2 block text-sm text-gray-700">
                      改进句子结构
                    </label>
                  </div>

                  <button
                    onClick={handleImproveReadability}
                    disabled={isImprovingReadability || !readabilityContent.trim()}
                    className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                  >
                    {isImprovingReadability ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        优化中...
                      </>
                    ) : (
                      <>
                        <BookOpen className="h-4 w-4 mr-2" />
                        优化可读性
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Results Section */}
            <div className="lg:col-span-2">
              {readabilityResult && (
                <div className="space-y-6">
                  {/* Header */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">可读性优化结果</h3>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => copyToClipboard(readabilityResult.improvedContent)}
                          className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors flex items-center"
                        >
                          <Copy className="h-3 w-3 mr-1" />
                          复制
                        </button>
                        <button
                          onClick={() => exportData(readabilityResult, 'readability-result')}
                          className="px-3 py-1 text-sm bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors flex items-center"
                        >
                          <Download className="h-3 w-3 mr-1" />
                          导出
                        </button>
                      </div>
                    </div>

                    {readabilityResult.error && (
                      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                        <span className="text-sm text-yellow-800">{readabilityResult.error}</span>
                      </div>
                    )}

                    {/* Content Comparison */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">原始内容</h4>
                        <div className="bg-gray-50 p-4 rounded-lg h-40 overflow-y-auto">
                          <p className="text-sm text-gray-700 whitespace-pre-wrap">{readabilityResult.originalContent}</p>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">优化后内容</h4>
                        <div className="bg-purple-50 p-4 rounded-lg h-40 overflow-y-auto">
                          <p className="text-sm text-gray-700 whitespace-pre-wrap">{readabilityResult.improvedContent}</p>
                        </div>
                      </div>
                    </div>

                    {/* Readability Metrics */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{readabilityResult.readabilityMetrics.fleschScore}</div>
                        <div className="text-sm text-gray-600">Flesch评分</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{readabilityResult.readabilityMetrics.averageSentenceLength}</div>
                        <div className="text-sm text-gray-600">平均句长</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">{readabilityResult.readabilityMetrics.averageWordsPerSentence}</div>
                        <div className="text-sm text-gray-600">句均词数</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">{readabilityResult.readabilityMetrics.complexWordsPercentage}%</div>
                        <div className="text-sm text-gray-600">复杂词比例</div>
                      </div>
                    </div>
                  </div>

                  {/* Improvements */}
                  {readabilityResult.improvements && readabilityResult.improvements.length > 0 && (
                    <div className="bg-white rounded-lg shadow p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">具体改进</h3>
                      <div className="space-y-4">
                        {readabilityResult.improvements.map((improvement, index) => (
                          <div key={index} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex items-center mb-2">
                              <span className={`px-2 py-1 text-xs rounded-full ${getImprovementTypeColor(improvement.type)}`}>
                                {improvement.type === 'sentence' ? '句子' :
                                 improvement.type === 'vocabulary' ? '词汇' :
                                 improvement.type === 'structure' ? '结构' : '流畅性'}
                              </span>
                              <span className="ml-2 text-sm font-medium text-gray-900">{improvement.description}</span>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <h5 className="text-xs font-medium text-gray-700 mb-1">修改前:</h5>
                                <p className="text-sm text-gray-600 bg-red-50 p-2 rounded">{improvement.before}</p>
                              </div>
                              <div>
                                <h5 className="text-xs font-medium text-gray-700 mb-1">修改后:</h5>
                                <p className="text-sm text-gray-600 bg-green-50 p-2 rounded">{improvement.after}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Suggestions */}
                  {readabilityResult.suggestions && readabilityResult.suggestions.length > 0 && (
                    <div className="bg-white rounded-lg shadow p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">进一步建议</h3>
                      <ul className="space-y-2">
                        {readabilityResult.suggestions.map((suggestion, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-purple-600 mr-2">•</span>
                            <span className="text-sm text-gray-600">{suggestion}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Rewrite;