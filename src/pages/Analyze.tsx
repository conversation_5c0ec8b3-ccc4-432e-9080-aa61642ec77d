import React, { useState } from 'react';
import { Upload, Link, FileText, BarChart3, Lightbulb, History, Download } from 'lucide-react';
import { toast } from 'sonner';
import AnalysisChart from '../components/AnalysisChart';
import SuggestionCard from '../components/SuggestionCard';
import { useAuthStore } from '../stores/authStore';

interface AnalysisResult {
  scores: {
    structureClarity: number;
    citationValue: number;
    semanticAccuracy: number;
    informationDensity: number;
    readability: number;
  };
  summary: string;
  suggestions: Array<{
    type: string;
    originalText: string;
    suggestedText: string;
    confidence: number;
  }>;
}

interface ContentData {
  id: string;
  title: string;
  originalContent: string;
  contentType: string;
  sourceUrl?: string;
}

const Analyze: React.FC = () => {
  const { token } = useAuthStore();
  const [activeTab, setActiveTab] = useState<'text' | 'file' | 'url'>('text');
  const [content, setContent] = useState('');
  const [url, setUrl] = useState('');
  const [title, setTitle] = useState('');
  const [contentType, setContentType] = useState('article');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [contentData, setContentData] = useState<ContentData | null>(null);
  const [analysisHistory, setAnalysisHistory] = useState<any[]>([]);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const text = e.target?.result as string;
          setContent(text);
          setTitle(file.name.replace('.txt', ''));
        };
        reader.readAsText(file);
      } else {
        toast.error('请上传 .txt 格式的文件');
      }
    }
  };

  const handleUrlFetch = async () => {
    if (!url) {
      toast.error('请输入有效的URL');
      return;
    }

    // 基本URL格式验证
    try {
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        toast.error('请输入有效的HTTP或HTTPS链接');
        return;
      }
    } catch (urlError) {
      toast.error('URL格式无效，请检查链接格式');
      return;
    }

    try {
      setIsAnalyzing(true);
      toast.loading('正在抓取URL内容...', { id: 'url-fetch' });
      
      const response = await fetch('/api/content/fetch-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        let errorMessage = 'URL抓取失败';
        
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch {
          // 如果无法解析错误响应，使用状态码信息
          switch (response.status) {
            case 400:
              errorMessage = 'URL格式无效或请求参数错误';
              break;
            case 401:
              errorMessage = '请先登录后再使用此功能';
              break;
            case 403:
              errorMessage = '没有权限访问此功能';
              break;
            case 404:
              errorMessage = '请求的页面不存在';
              break;
            case 429:
              errorMessage = '请求过于频繁，请稍后重试';
              break;
            case 500:
              errorMessage = '服务器内部错误，请稍后重试';
              break;
            case 502:
            case 503:
            case 504:
              errorMessage = '服务暂时不可用，请稍后重试';
              break;
            default:
              errorMessage = `请求失败 (${response.status})`;
          }
        }
        
        throw new Error(errorMessage);
      }

      const result = await response.json();
      
      if (!result.content || !result.title) {
        throw new Error('抓取的内容为空或格式不正确');
      }
      
      setContent(result.content);
      setTitle(result.title);
      toast.success('URL内容抓取成功', { id: 'url-fetch' });
      
    } catch (error) {
      console.error('URL抓取错误:', error);
      
      let userMessage = 'URL抓取失败';
      
      if (error instanceof TypeError) {
        if (error.message.includes('fetch')) {
          userMessage = '网络连接失败，请检查网络连接后重试';
        } else {
          userMessage = '请求处理失败，请重试';
        }
      } else if (error.message) {
        userMessage = error.message;
      }
      
      // 根据错误类型提供具体的解决建议
      if (userMessage.includes('网络连接失败')) {
        toast.error(userMessage + '\n建议：检查网络连接或尝试其他URL', { id: 'url-fetch' });
      } else if (userMessage.includes('URL格式无效')) {
        toast.error(userMessage + '\n建议：确保URL以http://或https://开头', { id: 'url-fetch' });
      } else if (userMessage.includes('无法提取页面内容')) {
        toast.error(userMessage + '\n建议：该页面可能不支持内容抓取，请尝试复制粘贴内容', { id: 'url-fetch' });
      } else if (userMessage.includes('请求超时')) {
        toast.error(userMessage + '\n建议：网页响应较慢，请稍后重试', { id: 'url-fetch' });
      } else if (userMessage.includes('服务器响应错误')) {
        toast.error(userMessage + '\n建议：目标网站可能暂时不可用', { id: 'url-fetch' });
      } else {
        toast.error(userMessage, { id: 'url-fetch' });
      }
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleAnalyze = async () => {
    if (!content.trim()) {
      toast.error('请输入要分析的内容');
      return;
    }

    if (!title.trim()) {
      toast.error('请输入内容标题');
      return;
    }

    if (content.trim().length < 50) {
      toast.error('内容长度至少需要50个字符才能进行有效分析');
      return;
    }

    try {
      setIsAnalyzing(true);
      toast.loading('正在创建内容...', { id: 'analyze-process' });
      
      // 首先创建内容
      const createResponse = await fetch('/api/content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          title,
          originalContent: content,
          contentType,
          sourceUrl: activeTab === 'url' ? url : undefined,
        }),
      });

      if (!createResponse.ok) {
        let errorMessage = '创建内容失败';
        
        try {
          const errorData = await createResponse.json();
          errorMessage = errorData.message || errorMessage;
        } catch {
          switch (createResponse.status) {
            case 400:
              errorMessage = '内容格式无效，请检查输入内容';
              break;
            case 401:
              errorMessage = '请先登录后再使用此功能';
              break;
            case 403:
              errorMessage = '没有权限创建内容';
              break;
            case 429:
              errorMessage = '创建内容过于频繁，请稍后重试';
              break;
            case 500:
              errorMessage = '服务器内部错误，请稍后重试';
              break;
            default:
              errorMessage = `创建内容失败 (${createResponse.status})`;
          }
        }
        
        throw new Error(errorMessage);
      }

      const createResult = await createResponse.json();
      
      if (!createResult.content || !createResult.content.id) {
        throw new Error('创建内容返回数据格式错误');
      }
      
      setContentData(createResult.content);
      toast.loading('正在进行GEO分析...', { id: 'analyze-process' });

      // 然后进行分析
      const analyzeResponse = await fetch(`/api/content/${createResult.content.id}/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({}),
      });

      if (!analyzeResponse.ok) {
        let errorMessage = '分析失败';
        
        try {
          const errorData = await analyzeResponse.json();
          errorMessage = errorData.message || errorMessage;
        } catch {
          switch (analyzeResponse.status) {
            case 400:
              errorMessage = '分析请求参数错误';
              break;
            case 401:
              errorMessage = '请先登录后再使用此功能';
              break;
            case 403:
              errorMessage = '没有权限进行内容分析';
              break;
            case 404:
              errorMessage = '要分析的内容不存在';
              break;
            case 429:
              errorMessage = '分析请求过于频繁，请稍后重试';
              break;
            case 500:
              errorMessage = 'AI分析服务暂时不可用，请稍后重试';
              break;
            case 502:
            case 503:
            case 504:
              errorMessage = '分析服务暂时不可用，请稍后重试';
              break;
            default:
              errorMessage = `分析失败 (${analyzeResponse.status})`;
          }
        }
        
        throw new Error(errorMessage);
      }

      const result = await analyzeResponse.json();
      
      if (!result || typeof result !== 'object') {
        throw new Error('分析结果格式错误');
      }
      
      setAnalysisResult(result);
      toast.success('内容分析完成！', { id: 'analyze-process' });
      
    } catch (error) {
      console.error('分析错误:', error);
      
      let userMessage = '分析失败';
      
      if (error instanceof TypeError) {
        if (error.message.includes('fetch')) {
          userMessage = '网络连接失败，请检查网络连接后重试';
        } else {
          userMessage = '请求处理失败，请重试';
        }
      } else if (error.message) {
        userMessage = error.message;
      }
      
      // 根据错误类型提供具体的解决建议
      if (userMessage.includes('网络连接失败')) {
        toast.error(userMessage + '\n建议：检查网络连接状态', { id: 'analyze-process' });
      } else if (userMessage.includes('请先登录')) {
        toast.error(userMessage + '\n建议：请刷新页面重新登录', { id: 'analyze-process' });
      } else if (userMessage.includes('AI分析服务')) {
        toast.error(userMessage + '\n建议：AI服务可能正在维护，请稍后重试', { id: 'analyze-process' });
      } else if (userMessage.includes('过于频繁')) {
        toast.error(userMessage + '\n建议：请等待几分钟后再试', { id: 'analyze-process' });
      } else {
        toast.error(userMessage, { id: 'analyze-process' });
      }
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBarColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">内容 GEO 分析</h1>
          <p className="text-gray-600">分析您的内容并获得专业的 GEO 优化建议</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：内容输入 */}
          <div className="space-y-6">
            {/* 输入方式选择 */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">选择输入方式</h2>
              <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setActiveTab('text')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'text'
                      ? 'bg-white text-blue-600 shadow'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <FileText className="w-4 h-4 inline mr-2" />
                  文本输入
                </button>
                <button
                  onClick={() => setActiveTab('file')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'file'
                      ? 'bg-white text-blue-600 shadow'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Upload className="w-4 h-4 inline mr-2" />
                  文件上传
                </button>
                <button
                  onClick={() => setActiveTab('url')}
                  className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'url'
                      ? 'bg-white text-blue-600 shadow'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Link className="w-4 h-4 inline mr-2" />
                  URL 抓取
                </button>
              </div>
            </div>

            {/* 内容输入区域 */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">内容输入</h3>
              
              {/* 标题输入 */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  内容标题
                </label>
                <input
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入内容标题"
                />
              </div>

              {/* 内容类型选择 */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  内容类型
                </label>
                <select
                  value={contentType}
                  onChange={(e) => setContentType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="article">文章</option>
                  <option value="blog">博客</option>
                  <option value="product">产品描述</option>
                  <option value="news">新闻</option>
                  <option value="other">其他</option>
                </select>
              </div>

              {/* 根据选择的标签显示不同的输入方式 */}
              {activeTab === 'text' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    内容文本
                  </label>
                  <textarea
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    rows={12}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入要分析的内容..."
                  />
                </div>
              )}

              {activeTab === 'file' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    上传文件
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">点击上传或拖拽文件到此处</p>
                    <p className="text-sm text-gray-500 mb-4">支持 .txt 格式文件</p>
                    <input
                      type="file"
                      accept=".txt"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="file-upload"
                    />
                    <label
                      htmlFor="file-upload"
                      className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 cursor-pointer"
                    >
                      选择文件
                    </label>
                  </div>
                  {content && (
                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        文件内容预览
                      </label>
                      <textarea
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        rows={8}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'url' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    网页 URL
                  </label>
                  <div className="flex space-x-2 mb-4">
                    <input
                      type="url"
                      value={url}
                      onChange={(e) => setUrl(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="https://example.com/article"
                    />
                    <button
                      onClick={handleUrlFetch}
                      disabled={isAnalyzing}
                      className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
                    >
                      {isAnalyzing ? '抓取中...' : '抓取'}
                    </button>
                  </div>
                  {content && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        抓取内容预览
                      </label>
                      <textarea
                        value={content}
                        onChange={(e) => setContent(e.target.value)}
                        rows={8}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  )}
                </div>
              )}

              {/* 分析按钮 */}
              <div className="mt-6">
                <button
                  onClick={handleAnalyze}
                  disabled={isAnalyzing || !content.trim() || !title.trim()}
                  className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {isAnalyzing ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      分析中...
                    </>
                  ) : (
                    <>
                      <BarChart3 className="w-5 h-5 mr-2" />
                      开始分析
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* 右侧：分析结果 */}
          <div className="space-y-6">
            {analysisResult ? (
              <>
                {/* GEO 评分 */}
                <AnalysisChart scores={analysisResult.scores} />

                {/* 分析摘要 */}
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">分析摘要</h3>
                  <p className="text-gray-700 leading-relaxed">{analysisResult.summary}</p>
                </div>

                {/* 优化建议 */}
                <SuggestionCard suggestions={analysisResult.suggestions} />

                {/* 操作按钮 */}
                <div className="bg-white rounded-lg shadow p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">操作</h3>
                  <div className="flex space-x-4">
                    <button className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 flex items-center justify-center">
                      <Download className="w-4 h-4 mr-2" />
                      导出报告
                    </button>
                    <button className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 flex items-center justify-center">
                      <History className="w-4 h-4 mr-2" />
                      查看历史
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <div className="bg-white rounded-lg shadow p-6 text-center">
                <BarChart3 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">等待分析</h3>
                <p className="text-gray-600">请在左侧输入内容并点击"开始分析"按钮</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analyze;