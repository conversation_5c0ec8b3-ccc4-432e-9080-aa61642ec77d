import React, { useState } from 'react';
import { Search, Target, TrendingUp, Copy, Download, Filter, BarChart3 } from 'lucide-react';
import { toast } from 'sonner';
import { useAuthStore } from '../stores/authStore';

interface Keyword {
  keyword: string;
  type: 'primary' | 'long-tail' | 'semantic';
  searchVolume: 'high' | 'medium' | 'low';
  difficulty: 'easy' | 'medium' | 'hard';
  relevance: number;
}

interface Question {
  question: string;
  type: 'what' | 'how' | 'why' | 'when' | 'where' | 'who';
  intent: 'informational' | 'navigational' | 'transactional';
  difficulty: 'easy' | 'medium' | 'hard';
  popularity: 'high' | 'medium' | 'low';
}

interface KeywordResult {
  keywords: Keyword[];
  totalCount: number;
  generatedAt: string;
  recommendations: string[];
  error?: string;
}

interface QuestionResult {
  questions: Question[];
  totalCount: number;
  questionType: string;
  generatedAt: string;
  recommendations: string[];
  error?: string;
}

interface TrendData {
  keyword: string;
  trend: 'rising' | 'stable';
  changePercent: number;
  seasonality: 'seasonal' | 'stable';
}

interface TrendResult {
  trends: TrendData[];
  summary: {
    risingKeywords: number;
    stableKeywords: number;
    seasonalKeywords: number;
  };
}

const Keywords: React.FC = () => {
  const { token } = useAuthStore();
  const [activeTab, setActiveTab] = useState<'keywords' | 'questions' | 'trends'>('keywords');
  
  // Keywords tab state
  const [content, setContent] = useState('');
  const [targetAudience, setTargetAudience] = useState('');
  const [industry, setIndustry] = useState('');
  const [keywordCount, setKeywordCount] = useState(20);
  const [isGeneratingKeywords, setIsGeneratingKeywords] = useState(false);
  const [keywordResult, setKeywordResult] = useState<KeywordResult | null>(null);
  const [keywordFilter, setKeywordFilter] = useState<string>('all');
  
  // Questions tab state
  const [questionContent, setQuestionContent] = useState('');
  const [questionType, setQuestionType] = useState('all');
  const [questionCount, setQuestionCount] = useState(15);
  const [isGeneratingQuestions, setIsGeneratingQuestions] = useState(false);
  const [questionResult, setQuestionResult] = useState<QuestionResult | null>(null);
  const [questionFilter, setQuestionFilter] = useState<string>('all');
  
  // Trends tab state
  const [trendKeywords, setTrendKeywords] = useState('');
  const [isAnalyzingTrends, setIsAnalyzingTrends] = useState(false);
  const [trendResult, setTrendResult] = useState<TrendResult | null>(null);

  const questionTypes = [
    { value: 'all', label: '所有类型' },
    { value: 'what', label: '什么 (What)' },
    { value: 'how', label: '如何 (How)' },
    { value: 'why', label: '为什么 (Why)' },
    { value: 'when', label: '何时 (When)' },
    { value: 'where', label: '哪里 (Where)' },
    { value: 'who', label: '谁 (Who)' }
  ];

  const handleGenerateKeywords = async () => {
    if (!content.trim()) {
      toast.error('请输入内容');
      return;
    }

    setIsGeneratingKeywords(true);

    try {
      const response = await fetch('/api/keyword/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          content,
          targetAudience,
          industry,
          keywordCount
        })
      });

      if (!response.ok) {
        throw new Error('生成失败');
      }

      const data = await response.json();
      setKeywordResult(data);
      toast.success('关键词生成成功！');
    } catch (error) {
      console.error('生成失败:', error);
      toast.error('生成失败，请重试');
    } finally {
      setIsGeneratingKeywords(false);
    }
  };

  const handleGenerateQuestions = async () => {
    if (!questionContent.trim()) {
      toast.error('请输入内容');
      return;
    }

    setIsGeneratingQuestions(true);

    try {
      const response = await fetch('/api/keyword/questions/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          content: questionContent,
          questionType,
          questionCount
        })
      });

      if (!response.ok) {
        throw new Error('生成失败');
      }

      const data = await response.json();
      setQuestionResult(data);
      toast.success('问题生成成功！');
    } catch (error) {
      console.error('生成失败:', error);
      toast.error('生成失败，请重试');
    } finally {
      setIsGeneratingQuestions(false);
    }
  };

  const handleAnalyzeTrends = async () => {
    const keywords = trendKeywords.split('\n').filter(k => k.trim()).map(k => k.trim());
    
    if (keywords.length === 0) {
      toast.error('请输入关键词');
      return;
    }

    if (keywords.length > 20) {
      toast.error('最多支持20个关键词');
      return;
    }

    setIsAnalyzingTrends(true);

    try {
      const response = await fetch('/api/keyword/trends/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ keywords })
      });

      if (!response.ok) {
        throw new Error('分析失败');
      }

      const data = await response.json();
      setTrendResult(data);
      toast.success('趋势分析完成！');
    } catch (error) {
      console.error('分析失败:', error);
      toast.error('分析失败，请重试');
    } finally {
      setIsAnalyzingTrends(false);
    }
  };

  const getKeywordTypeColor = (type: string) => {
    const colors = {
      primary: 'bg-blue-100 text-blue-800',
      'long-tail': 'bg-green-100 text-green-800',
      semantic: 'bg-purple-100 text-purple-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getDifficultyColor = (difficulty: string) => {
    const colors = {
      easy: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      hard: 'bg-red-100 text-red-800'
    };
    return colors[difficulty] || 'bg-gray-100 text-gray-800';
  };

  const getVolumeColor = (volume: string) => {
    const colors = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800'
    };
    return colors[volume] || 'bg-gray-100 text-gray-800';
  };

  const getQuestionTypeColor = (type: string) => {
    const colors = {
      what: 'bg-blue-100 text-blue-800',
      how: 'bg-green-100 text-green-800',
      why: 'bg-purple-100 text-purple-800',
      when: 'bg-orange-100 text-orange-800',
      where: 'bg-pink-100 text-pink-800',
      who: 'bg-indigo-100 text-indigo-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('已复制到剪贴板');
  };

  const exportData = (data: any, filename: string) => {
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${filename}-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('文件导出成功');
  };

  const filteredKeywords = keywordResult?.keywords.filter(keyword => {
    if (keywordFilter === 'all') return true;
    return keyword.type === keywordFilter;
  }) || [];

  const filteredQuestions = questionResult?.questions.filter(question => {
    if (questionFilter === 'all') return true;
    return question.type === questionFilter;
  }) || [];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">关键词 &amp; 问题生成器</h1>
          <p className="text-gray-600">生成SEO关键词、用户问题，并分析关键词趋势</p>
        </div>

        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('keywords')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'keywords'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Search className="h-4 w-4 inline mr-2" />
                关键词生成
              </button>
              <button
                onClick={() => setActiveTab('questions')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'questions'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Target className="h-4 w-4 inline mr-2" />
                问题生成
              </button>
              <button
                onClick={() => setActiveTab('trends')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'trends'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <TrendingUp className="h-4 w-4 inline mr-2" />
                趋势分析
              </button>
            </nav>
          </div>
        </div>

        {/* Keywords Tab */}
        {activeTab === 'keywords' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Input Section */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow p-6 sticky top-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">关键词生成</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      内容文本
                    </label>
                    <textarea
                      value={content}
                      onChange={(e) => setContent(e.target.value)}
                      placeholder="请输入需要生成关键词的内容..."
                      className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      目标受众 (可选)
                    </label>
                    <input
                      type="text"
                      value={targetAudience}
                      onChange={(e) => setTargetAudience(e.target.value)}
                      placeholder="例如：技术开发者、企业主等"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      行业 (可选)
                    </label>
                    <input
                      type="text"
                      value={industry}
                      onChange={(e) => setIndustry(e.target.value)}
                      placeholder="例如：科技、教育、医疗等"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      关键词数量: {keywordCount}
                    </label>
                    <input
                      type="range"
                      min="10"
                      max="50"
                      value={keywordCount}
                      onChange={(e) => setKeywordCount(Number(e.target.value))}
                      className="w-full"
                    />
                  </div>

                  <button
                    onClick={handleGenerateKeywords}
                    disabled={isGeneratingKeywords || !content.trim()}
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                  >
                    {isGeneratingKeywords ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        生成中...
                      </>
                    ) : (
                      <>
                        <Search className="h-4 w-4 mr-2" />
                        生成关键词
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Results Section */}
            <div className="lg:col-span-2">
              {keywordResult && (
                <div className="space-y-6">
                  {/* Header */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">
                        关键词结果 ({keywordResult.totalCount}个)
                      </h3>
                      <div className="flex items-center space-x-2">
                        <select
                          value={keywordFilter}
                          onChange={(e) => setKeywordFilter(e.target.value)}
                          className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="all">所有类型</option>
                          <option value="primary">主要关键词</option>
                          <option value="long-tail">长尾关键词</option>
                          <option value="semantic">语义相关</option>
                        </select>
                        <button
                          onClick={() => copyToClipboard(filteredKeywords.map(k => k.keyword).join('\n'))}
                          className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors flex items-center"
                        >
                          <Copy className="h-3 w-3 mr-1" />
                          复制
                        </button>
                        <button
                          onClick={() => exportData(keywordResult, 'keywords')}
                          className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors flex items-center"
                        >
                          <Download className="h-3 w-3 mr-1" />
                          导出
                        </button>
                      </div>
                    </div>

                    {keywordResult.error && (
                      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                        <span className="text-sm text-yellow-800">{keywordResult.error}</span>
                      </div>
                    )}

                    {/* Keywords Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {filteredKeywords.map((keyword, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                          <div className="flex items-start justify-between mb-2">
                            <h4 className="font-medium text-gray-900">{keyword.keyword}</h4>
                            <button
                              onClick={() => copyToClipboard(keyword.keyword)}
                              className="text-gray-400 hover:text-gray-600"
                            >
                              <Copy className="h-4 w-4" />
                            </button>
                          </div>
                          <div className="flex flex-wrap gap-2 mb-2">
                            <span className={`px-2 py-1 text-xs rounded-full ${getKeywordTypeColor(keyword.type)}`}>
                              {keyword.type === 'primary' ? '主要' : keyword.type === 'long-tail' ? '长尾' : '语义'}
                            </span>
                            <span className={`px-2 py-1 text-xs rounded-full ${getVolumeColor(keyword.searchVolume)}`}>
                              {keyword.searchVolume === 'high' ? '高搜索量' : keyword.searchVolume === 'medium' ? '中搜索量' : '低搜索量'}
                            </span>
                            <span className={`px-2 py-1 text-xs rounded-full ${getDifficultyColor(keyword.difficulty)}`}>
                              {keyword.difficulty === 'easy' ? '易' : keyword.difficulty === 'medium' ? '中' : '难'}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-sm text-gray-600 mr-2">相关性:</span>
                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full" 
                                style={{ width: `${keyword.relevance * 10}%` }}
                              ></div>
                            </div>
                            <span className="text-sm text-gray-600 ml-2">{keyword.relevance}/10</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Recommendations */}
                  {keywordResult.recommendations && (
                    <div className="bg-white rounded-lg shadow p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">使用建议</h3>
                      <ul className="space-y-2">
                        {keywordResult.recommendations.map((recommendation, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-blue-600 mr-2">•</span>
                            <span className="text-sm text-gray-600">{recommendation}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Questions Tab */}
        {activeTab === 'questions' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Input Section */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow p-6 sticky top-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">问题生成</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      内容文本
                    </label>
                    <textarea
                      value={questionContent}
                      onChange={(e) => setQuestionContent(e.target.value)}
                      placeholder="请输入需要生成问题的内容..."
                      className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      问题类型
                    </label>
                    <select
                      value={questionType}
                      onChange={(e) => setQuestionType(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {questionTypes.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      问题数量: {questionCount}
                    </label>
                    <input
                      type="range"
                      min="5"
                      max="30"
                      value={questionCount}
                      onChange={(e) => setQuestionCount(Number(e.target.value))}
                      className="w-full"
                    />
                  </div>

                  <button
                    onClick={handleGenerateQuestions}
                    disabled={isGeneratingQuestions || !questionContent.trim()}
                    className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                  >
                    {isGeneratingQuestions ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        生成中...
                      </>
                    ) : (
                      <>
                        <Target className="h-4 w-4 mr-2" />
                        生成问题
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Results Section */}
            <div className="lg:col-span-2">
              {questionResult && (
                <div className="space-y-6">
                  {/* Header */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">
                        问题结果 ({questionResult.totalCount}个)
                      </h3>
                      <div className="flex items-center space-x-2">
                        <select
                          value={questionFilter}
                          onChange={(e) => setQuestionFilter(e.target.value)}
                          className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="all">所有类型</option>
                          <option value="what">什么</option>
                          <option value="how">如何</option>
                          <option value="why">为什么</option>
                          <option value="when">何时</option>
                          <option value="where">哪里</option>
                          <option value="who">谁</option>
                        </select>
                        <button
                          onClick={() => copyToClipboard(filteredQuestions.map(q => q.question).join('\n'))}
                          className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors flex items-center"
                        >
                          <Copy className="h-3 w-3 mr-1" />
                          复制
                        </button>
                        <button
                          onClick={() => exportData(questionResult, 'questions')}
                          className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors flex items-center"
                        >
                          <Download className="h-3 w-3 mr-1" />
                          导出
                        </button>
                      </div>
                    </div>

                    {questionResult.error && (
                      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                        <span className="text-sm text-yellow-800">{questionResult.error}</span>
                      </div>
                    )}

                    {/* Questions List */}
                    <div className="space-y-3">
                      {filteredQuestions.map((question, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                          <div className="flex items-start justify-between mb-2">
                            <h4 className="font-medium text-gray-900 flex-1">{question.question}</h4>
                            <button
                              onClick={() => copyToClipboard(question.question)}
                              className="text-gray-400 hover:text-gray-600 ml-2"
                            >
                              <Copy className="h-4 w-4" />
                            </button>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            <span className={`px-2 py-1 text-xs rounded-full ${getQuestionTypeColor(question.type)}`}>
                              {question.type.toUpperCase()}
                            </span>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              question.intent === 'informational' ? 'bg-blue-100 text-blue-800' :
                              question.intent === 'navigational' ? 'bg-green-100 text-green-800' :
                              'bg-purple-100 text-purple-800'
                            }`}>
                              {question.intent === 'informational' ? '信息性' :
                               question.intent === 'navigational' ? '导航性' : '交易性'}
                            </span>
                            <span className={`px-2 py-1 text-xs rounded-full ${getDifficultyColor(question.difficulty)}`}>
                              {question.difficulty === 'easy' ? '易答' : question.difficulty === 'medium' ? '中等' : '困难'}
                            </span>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              question.popularity === 'high' ? 'bg-red-100 text-red-800' :
                              question.popularity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                            }`}>
                              {question.popularity === 'high' ? '高热度' :
                               question.popularity === 'medium' ? '中热度' : '低热度'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Recommendations */}
                  {questionResult.recommendations && (
                    <div className="bg-white rounded-lg shadow p-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">使用建议</h3>
                      <ul className="space-y-2">
                        {questionResult.recommendations.map((recommendation, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-green-600 mr-2">•</span>
                            <span className="text-sm text-gray-600">{recommendation}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Trends Tab */}
        {activeTab === 'trends' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Input Section */}
            <div>
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">关键词趋势分析</h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      关键词列表 (每行一个，最多20个)
                    </label>
                    <textarea
                      value={trendKeywords}
                      onChange={(e) => setTrendKeywords(e.target.value)}
                      placeholder="SEO优化\n内容营销\n搜索引擎\n关键词研究\n..."
                      className="w-full h-40 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                    />
                  </div>

                  <button
                    onClick={handleAnalyzeTrends}
                    disabled={isAnalyzingTrends || !trendKeywords.trim()}
                    className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                  >
                    {isAnalyzingTrends ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        分析中...
                      </>
                    ) : (
                      <>
                        <BarChart3 className="h-4 w-4 mr-2" />
                        分析趋势
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Results Section */}
            <div>
              {trendResult && (
                <div className="space-y-6">
                  {/* Summary */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">趋势概览</h3>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">{trendResult.summary.risingKeywords}</div>
                        <div className="text-sm text-gray-600">上升趋势</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{trendResult.summary.stableKeywords}</div>
                        <div className="text-sm text-gray-600">稳定趋势</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">{trendResult.summary.seasonalKeywords}</div>
                        <div className="text-sm text-gray-600">季节性</div>
                      </div>
                    </div>
                  </div>

                  {/* Detailed Trends */}
                  <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">详细趋势</h3>
                      <button
                        onClick={() => exportData(trendResult, 'trends')}
                        className="px-3 py-1 text-sm bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors flex items-center"
                      >
                        <Download className="h-3 w-3 mr-1" />
                        导出
                      </button>
                    </div>
                    
                    <div className="space-y-3">
                      {trendResult.trends.map((trend, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-gray-900">{trend.keyword}</h4>
                            <div className="flex items-center space-x-2">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                trend.trend === 'rising' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                              }`}>
                                {trend.trend === 'rising' ? '上升' : '稳定'}
                              </span>
                              {trend.seasonality === 'seasonal' && (
                                <span className="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800">
                                  季节性
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center">
                            <span className="text-sm text-gray-600 mr-2">变化:</span>
                            <span className={`text-sm font-medium ${
                              trend.changePercent > 0 ? 'text-green-600' : 
                              trend.changePercent < 0 ? 'text-red-600' : 'text-gray-600'
                            }`}>
                              {trend.changePercent > 0 ? '+' : ''}{trend.changePercent}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Keywords;