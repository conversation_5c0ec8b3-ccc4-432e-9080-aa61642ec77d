import React from 'react';
import { BarChart3, TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface AnalysisChartProps {
  scores: {
    structureClarity: number;
    citationValue: number;
    semanticAccuracy: number;
    informationDensity: number;
    readability: number;
  };
}

const AnalysisChart: React.FC<AnalysisChartProps> = ({ scores }) => {
  const scoreLabels: Record<string, string> = {
    structureClarity: '结构清晰度',
    citationValue: '引用价值',
    semanticAccuracy: '语义准确性',
    informationDensity: '信息密度',
    readability: '可读性',
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBarColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 80) return <TrendingUp className="w-4 h-4 text-green-600" />;
    if (score >= 60) return <Minus className="w-4 h-4 text-yellow-600" />;
    return <TrendingDown className="w-4 h-4 text-red-600" />;
  };

  const getScoreLevel = (score: number) => {
    if (score >= 80) return '优秀';
    if (score >= 60) return '良好';
    return '需改进';
  };

  const averageScore = Math.round(
    Object.values(scores).reduce((sum, score) => sum + score, 0) / Object.values(scores).length
  );

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <BarChart3 className="w-5 h-5 mr-2" />
          GEO 评分分析
        </h3>
        <div className="text-right">
          <div className="text-2xl font-bold text-gray-900">{averageScore}</div>
          <div className="text-sm text-gray-500">综合评分</div>
        </div>
      </div>

      <div className="space-y-4">
        {Object.entries(scores).map(([key, score]) => (
          <div key={key} className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                {getScoreIcon(score)}
                <span className="text-sm font-medium text-gray-700">
                  {scoreLabels[key]}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`text-sm font-semibold ${getScoreColor(score)}`}>
                  {score}/100
                </span>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  score >= 80 ? 'bg-green-100 text-green-800' :
                  score >= 60 ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {getScoreLevel(score)}
                </span>
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-500 ${getScoreBarColor(score)}`}
                style={{ width: `${score}%` }}
              ></div>
            </div>
          </div>
        ))}
      </div>

      {/* 评分说明 */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-2">评分说明</h4>
        <div className="grid grid-cols-3 gap-4 text-xs">
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span className="text-gray-600">80-100: 优秀</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-yellow-500 rounded"></div>
            <span className="text-gray-600">60-79: 良好</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-red-500 rounded"></div>
            <span className="text-gray-600">0-59: 需改进</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalysisChart;