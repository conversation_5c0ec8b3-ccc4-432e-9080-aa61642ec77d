import React, { useState } from 'react';
import { <PERSON>bul<PERSON>, Co<PERSON>, Check, ChevronDown, ChevronUp } from 'lucide-react';
import { toast } from 'sonner';

interface Suggestion {
  type: string;
  originalText: string;
  suggestedText: string;
  confidence: number;
}

interface SuggestionCardProps {
  suggestions: Suggestion[];
}

const SuggestionCard: React.FC<SuggestionCardProps> = ({ suggestions }) => {
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());
  const [copiedItems, setCopiedItems] = useState<Set<number>>(new Set());

  const toggleExpanded = (index: number) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedItems(newExpanded);
  };

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedItems(new Set([...copiedItems, index]));
      toast.success('已复制到剪贴板');
      
      // 3秒后重置复制状态
      setTimeout(() => {
        setCopiedItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(index);
          return newSet;
        });
      }, 3000);
    } catch (error) {
      toast.error('复制失败');
    }
  };

  const getSuggestionTypeLabel = (type: string) => {
    const typeLabels: Record<string, { label: string; color: string }> = {
      structure: { label: '结构优化', color: 'bg-blue-100 text-blue-800' },
      citation: { label: '引用优化', color: 'bg-green-100 text-green-800' },
      semantic: { label: '语义优化', color: 'bg-purple-100 text-purple-800' },
      readability: { label: '可读性', color: 'bg-orange-100 text-orange-800' },
      keyword: { label: '关键词', color: 'bg-pink-100 text-pink-800' },
      default: { label: '通用优化', color: 'bg-gray-100 text-gray-800' },
    };
    return typeLabels[type] || typeLabels.default;
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (!suggestions || suggestions.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Lightbulb className="w-5 h-5 mr-2" />
          优化建议
        </h3>
        <div className="text-center py-8">
          <Lightbulb className="w-12 h-12 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500">暂无优化建议</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Lightbulb className="w-5 h-5 mr-2" />
          优化建议
        </h3>
        <span className="text-sm text-gray-500">
          共 {suggestions.length} 条建议
        </span>
      </div>
      
      <div className="space-y-4">
        {suggestions.map((suggestion, index) => {
          const isExpanded = expandedItems.has(index);
          const isCopied = copiedItems.has(index);
          const typeInfo = getSuggestionTypeLabel(suggestion.type);
          
          return (
            <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
              {/* 建议头部 */}
              <div className="p-4 bg-gray-50">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex items-center space-x-2">
                    <span className={`text-xs font-medium px-2 py-1 rounded-full ${typeInfo.color}`}>
                      {typeInfo.label}
                    </span>
                    <span className={`text-sm font-medium ${getConfidenceColor(suggestion.confidence)}`}>
                      置信度: {Math.round(suggestion.confidence * 100)}%
                    </span>
                  </div>
                  <button
                    onClick={() => toggleExpanded(index)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    {isExpanded ? (
                      <ChevronUp className="w-4 h-4" />
                    ) : (
                      <ChevronDown className="w-4 h-4" />
                    )}
                  </button>
                </div>
                
                {/* 建议预览 */}
                <div className="text-sm text-gray-600">
                  {suggestion.suggestedText.length > 100 && !isExpanded
                    ? `${suggestion.suggestedText.substring(0, 100)}...`
                    : suggestion.suggestedText
                  }
                </div>
              </div>
              
              {/* 展开的详细内容 */}
              {isExpanded && (
                <div className="p-4 space-y-4">
                  {/* 原文 */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">原文:</span>
                      <button
                        onClick={() => copyToClipboard(suggestion.originalText, index * 2)}
                        className="text-gray-400 hover:text-gray-600 transition-colors"
                        title="复制原文"
                      >
                        {copiedItems.has(index * 2) ? (
                          <Check className="w-4 h-4 text-green-600" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                    <div className="text-sm text-gray-600 bg-red-50 p-3 rounded border-l-4 border-red-200">
                      {suggestion.originalText}
                    </div>
                  </div>
                  
                  {/* 建议修改 */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">建议修改:</span>
                      <button
                        onClick={() => copyToClipboard(suggestion.suggestedText, index * 2 + 1)}
                        className="text-gray-400 hover:text-gray-600 transition-colors"
                        title="复制建议"
                      >
                        {copiedItems.has(index * 2 + 1) ? (
                          <Check className="w-4 h-4 text-green-600" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                    <div className="text-sm text-gray-600 bg-green-50 p-3 rounded border-l-4 border-green-200">
                      {suggestion.suggestedText}
                    </div>
                  </div>
                  
                  {/* 置信度条 */}
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-xs text-gray-500">建议可信度</span>
                      <span className={`text-xs font-medium ${getConfidenceColor(suggestion.confidence)}`}>
                        {Math.round(suggestion.confidence * 100)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        className={`h-1.5 rounded-full transition-all duration-300 ${
                          suggestion.confidence >= 0.8 ? 'bg-green-500' :
                          suggestion.confidence >= 0.6 ? 'bg-yellow-500' :
                          'bg-red-500'
                        }`}
                        style={{ width: `${suggestion.confidence * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
      
      {/* 批量操作 */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <button
            onClick={() => {
              const allSuggestions = suggestions.map(s => s.suggestedText).join('\n\n');
              copyToClipboard(allSuggestions, -1);
            }}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center text-sm"
          >
            <Copy className="w-4 h-4 mr-2" />
            复制所有建议
          </button>
          <button
            onClick={() => setExpandedItems(new Set(suggestions.map((_, i) => i)))}
            className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors text-sm"
          >
            展开全部
          </button>
          <button
            onClick={() => setExpandedItems(new Set())}
            className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors text-sm"
          >
            收起全部
          </button>
        </div>
      </div>
    </div>
  );
};

export default SuggestionCard;