import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { Toaster } from 'sonner';
import Home from "@/pages/Home";
import Login from "@/pages/Login";
import Register from "@/pages/Register";
import Dashboard from "@/pages/Dashboard";
import Analyze from "@/pages/Analyze";
import Schema from "@/pages/Schema";
import Keywords from "@/pages/Keywords";
import Rewrite from "@/pages/Rewrite";

export default function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/analyze" element={<Analyze />} />
        <Route path="/analysis" element={<Analyze />} />
          <Route path="/schema" element={<Schema />} />
          <Route path="/keywords" element={<Keywords />} />
          <Route path="/rewrite" element={<Rewrite />} />
      </Routes>
      <Toaster position="top-right" richColors />
    </Router>
  );
}
